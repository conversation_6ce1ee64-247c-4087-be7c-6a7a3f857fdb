"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/ui/kpis/maintance-pie-chart/maintance-pie-chart.tsx":
/*!*********************************************************************!*\
  !*** ./src/app/ui/kpis/maintance-pie-chart/maintance-pie-chart.tsx ***!
  \*********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ MaintenancePieChart; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _components_pie_chart__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/pie-chart */ \"(app-pages-browser)/./src/components/pie-chart.tsx\");\n/* harmony import */ var _queries__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./queries */ \"(app-pages-browser)/./src/app/ui/kpis/maintance-pie-chart/queries.ts\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction MaintenancePieChart() {\n    _s();\n    const [maintenanceChecks, setMaintenanceChecks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [keywordFilter, setKeywordFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [edit_task, setEdit_task] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [overdueTasks, setOverdueTasks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [upcomingTasks, setUpcomingTasks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [extractedDays, setExtractedDays] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [under30, setUnder30] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [between30and90, setBetween30and90] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [over90, setOver90] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const init_permissions = ()=>{\n        if (permissions) {\n            if ((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_3__.hasPermission)(\"EDIT_TASK\", permissions)) {\n                setEdit_task(true);\n            } else {\n                setEdit_task(false);\n            }\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_3__.getPermissions);\n        init_permissions();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        init_permissions();\n    }, [\n        permissions\n    ]);\n    const [queryMaintenanceChecks] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_7__.useLazyQuery)(_queries__WEBPACK_IMPORTED_MODULE_5__.ReadComponentMaintenanceChecks, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Raw GraphQL Response:\", response);\n            const data = response.readComponentMaintenanceChecks.nodes;\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Extracted nodes data:\", data);\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Total maintenance checks received:\", (data === null || data === void 0 ? void 0 : data.length) || 0);\n            if (data) {\n                handleSetMaintenanceChecks(data);\n            } else {\n                console.warn(\"⚠️ [MaintenancePieChart] No data received from GraphQL query\");\n            }\n        },\n        onError: (error)=>{\n            console.error(\"❌ [MaintenancePieChart] queryMaintenanceChecks error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            // loadVessels()\n            loadMaintenanceChecks();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const loadMaintenanceChecks = async function() {\n        let searchFilter = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {\n            ...filter\n        }, searchkeywordFilter = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : keywordFilter;\n        if (searchkeywordFilter.length > 0) {\n            const promises = searchkeywordFilter.map(async (keywordFilter)=>{\n                return await queryMaintenanceChecks({\n                    variables: {\n                        filter: {\n                            ...searchFilter,\n                            ...keywordFilter\n                        }\n                    }\n                });\n            });\n            let responses = await Promise.all(promises);\n            // filter out empty results\n            responses = responses.filter((r)=>r.data.readComponentMaintenanceChecks.nodes.length > 0);\n            // flatten results\n            responses = responses.flatMap((r)=>r.data.readComponentMaintenanceChecks.nodes);\n            // filter out duplicates\n            responses = responses.filter((value, index, self)=>self.findIndex((v)=>v.id === value.id) === index);\n            handleSetMaintenanceChecks(responses);\n        } else {\n            await queryMaintenanceChecks({\n                variables: {\n                    filter: searchFilter\n                }\n            });\n        }\n    };\n    const handleSetMaintenanceChecks = (tasks)=>{\n        console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Processing tasks in handleSetMaintenanceChecks:\", tasks);\n        console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Total tasks before filtering:\", (tasks === null || tasks === void 0 ? void 0 : tasks.length) || 0);\n        const activeTasks = tasks.filter((task)=>{\n            const isActive = task.archived === false;\n            if (!isActive) {\n                console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Filtered out archived task:\", task.id);\n            }\n            return isActive;\n        }).map((task)=>{\n            const overDueInfo = (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_2__.isOverDueTask)(task);\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Task processing:\", {\n                id: task.id,\n                status: task.status,\n                expires: task.expires,\n                startDate: task.startDate,\n                isOverDueInfo: overDueInfo\n            });\n            return {\n                ...task,\n                isOverDue: overDueInfo\n            };\n        });\n        console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Active tasks after processing:\", activeTasks);\n        console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Total active tasks:\", activeTasks.length);\n        // Log status distribution\n        const statusDistribution = activeTasks.reduce((acc, task)=>{\n            var _task_isOverDue;\n            const status = ((_task_isOverDue = task.isOverDue) === null || _task_isOverDue === void 0 ? void 0 : _task_isOverDue.status) || \"Unknown\";\n            acc[status] = (acc[status] || 0) + 1;\n            return acc;\n        }, {});\n        console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Status distribution:\", statusDistribution);\n        setMaintenanceChecks(activeTasks);\n    // const appendedData: number[] = Array.from(\n    //     new Set(\n    //         activeTasks\n    //             .filter((item: any) => item.assignedToID > 0)\n    //             .map((item: any) => item.assignedToID),\n    //     ),\n    // )\n    // loadCrewMemberInfo(appendedData)\n    };\n    // const [queryCrewMemberInfo] = useLazyQuery(GET_CREW_BY_IDS, {\n    //     fetchPolicy: 'cache-and-network',\n    //     onCompleted: (response: any) => {\n    //         const data = response.readSeaLogsMembers.nodes\n    //         if (data) {\n    //             // setCrewInfo(data)\n    //         }\n    //     },\n    //     onError: (error) => {\n    //         console.error('queryCrewMemberInfo error', error)\n    //     },\n    // })\n    // const loadCrewMemberInfo = async (crewId: any) => {\n    //     await queryCrewMemberInfo({\n    //         variables: {\n    //             crewMemberIDs: crewId.length > 0 ? crewId : [0],\n    //         },\n    //     })\n    // }\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (maintenanceChecks) {\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Processing maintenanceChecks for overdue/upcoming tasks\");\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Total maintenance checks to process:\", maintenanceChecks.length);\n            // Log all task statuses and days for debugging\n            maintenanceChecks.forEach((task, index)=>{\n                var _task_isOverDue, _task_isOverDue1;\n                console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Task \".concat(index + 1, \":\"), {\n                    id: task.id,\n                    status: task.status,\n                    isOverDueStatus: (_task_isOverDue = task.isOverDue) === null || _task_isOverDue === void 0 ? void 0 : _task_isOverDue.status,\n                    isOverDueDays: (_task_isOverDue1 = task.isOverDue) === null || _task_isOverDue1 === void 0 ? void 0 : _task_isOverDue1.days,\n                    expires: task.expires\n                });\n            });\n            const overdueTasks = maintenanceChecks.filter((task)=>{\n                var _task_isOverDue, _task_isOverDue_days, _task_isOverDue1;\n                const isHighStatus = ((_task_isOverDue = task.isOverDue) === null || _task_isOverDue === void 0 ? void 0 : _task_isOverDue.status) === \"High\";\n                const hasDaysString = (_task_isOverDue1 = task.isOverDue) === null || _task_isOverDue1 === void 0 ? void 0 : (_task_isOverDue_days = _task_isOverDue1.days) === null || _task_isOverDue_days === void 0 ? void 0 : _task_isOverDue_days.includes(\"days\");\n                const isOverdue = isHighStatus && hasDaysString;\n                if (isHighStatus) {\n                    console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] High status task:\", {\n                        id: task.id,\n                        days: task.isOverDue.days,\n                        hasDaysString,\n                        isOverdue\n                    });\n                }\n                return isOverdue;\n            });\n            const upcomingTasks = maintenanceChecks.filter((task)=>{\n                var _task_isOverDue;\n                const isUpcoming = ((_task_isOverDue = task.isOverDue) === null || _task_isOverDue === void 0 ? void 0 : _task_isOverDue.status) === \"Upcoming\";\n                if (isUpcoming) {\n                    console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Upcoming task:\", {\n                        id: task.id,\n                        days: task.isOverDue.days\n                    });\n                }\n                return isUpcoming;\n            });\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Overdue tasks count:\", overdueTasks.length);\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Upcoming tasks count:\", upcomingTasks.length);\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Overdue tasks:\", overdueTasks);\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Upcoming tasks:\", upcomingTasks);\n            setOverdueTasks(overdueTasks.length);\n            setUpcomingTasks(upcomingTasks);\n        }\n    }, [\n        maintenanceChecks\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (upcomingTasks) {\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Processing upcomingTasks for day extraction\");\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Upcoming tasks to process:\", upcomingTasks.length);\n            const extractedDays = upcomingTasks.filter((task)=>{\n                var _task_isOverDue_days, _task_isOverDue, _task_isOverDue1;\n                const hasDaysString = (_task_isOverDue = task.isOverDue) === null || _task_isOverDue === void 0 ? void 0 : (_task_isOverDue_days = _task_isOverDue.days) === null || _task_isOverDue_days === void 0 ? void 0 : _task_isOverDue_days.includes(\"days\");\n                console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Checking task for days string:\", {\n                    id: task.id,\n                    days: (_task_isOverDue1 = task.isOverDue) === null || _task_isOverDue1 === void 0 ? void 0 : _task_isOverDue1.days,\n                    hasDaysString\n                });\n                return hasDaysString;\n            });\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Extracted days tasks count:\", extractedDays.length);\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Extracted days tasks:\", extractedDays);\n            setExtractedDays(extractedDays);\n        }\n    }, [\n        upcomingTasks\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (extractedDays) {\n            setUnder30(extractedDays.filter((task)=>{\n                const match = (task.isOverDue.days || \"\").match(/(\\d+)/);\n                const days = match ? parseInt(match[0], 10) : null;\n                return days !== null && days < 30;\n            }).length);\n            setBetween30and90(extractedDays.filter((task)=>{\n                const match = (task.isOverDue.days || \"\").match(/(\\d+)/);\n                const days = match ? parseInt(match[0], 10) : null;\n                return days !== null && days >= 30 && days <= 90;\n            }).length);\n            setOver90(extractedDays.filter((task)=>{\n                const match = (task.isOverDue.days || \"\").match(/(\\d+)/);\n                const days = match ? parseInt(match[0], 10) : null;\n                return days !== null && days > 90;\n            }).length);\n        }\n    }, [\n        extractedDays\n    ]);\n    const chartData = [\n        {\n            title: \"Tasks overdue\",\n            amount: overdueTasks,\n            fill: \"var(--color-overdue)\",\n            stroke: \"hsl(1, 97%, 60%)\"\n        },\n        {\n            title: \"Tasks due < 30\",\n            amount: under30,\n            fill: \"var(--color-thirtyDays)\",\n            stroke: \"hsl(205, 78%, 48%)\"\n        },\n        {\n            title: \"Tasks due 30 - 90\",\n            amount: between30and90,\n            fill: \"var(--color-thirtyToNinety)\",\n            stroke: \"hsl(205, 32%, 45%)\"\n        },\n        {\n            title: \"Tasks due > 90\",\n            amount: over90,\n            fill: \"var(--color-ninetyPlus)\",\n            stroke: \"hsl(174, 100%, 40%)\"\n        }\n    ];\n    const chartConfig = {\n        amount: {\n            label: \"Amount\"\n        },\n        overdue: {\n            label: \"Tasks overdue\",\n            color: \"var(--chart-1)\"\n        },\n        thirtyDays: {\n            label: \"Tasks due under 30-days\",\n            color: \"var(--chart-3)\"\n        },\n        thirtyToNinety: {\n            label: \"Tasks due 30-90 days\",\n            color: \"var(--chart-4)\"\n        },\n        ninetyPlus: {\n            label: \"Tasks due 90+ days\",\n            color: \"var(--chart-5)\"\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col text-center\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.P, {\n                children: \"Tasks due - days\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\kpis\\\\maintance-pie-chart\\\\maintance-pie-chart.tsx\",\n                lineNumber: 395,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_pie_chart__WEBPACK_IMPORTED_MODULE_4__.PieChartComponent, {\n                chartData: chartData,\n                chartConfig: chartConfig\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\kpis\\\\maintance-pie-chart\\\\maintance-pie-chart.tsx\",\n                lineNumber: 396,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\kpis\\\\maintance-pie-chart\\\\maintance-pie-chart.tsx\",\n        lineNumber: 394,\n        columnNumber: 9\n    }, this);\n}\n_s(MaintenancePieChart, \"M4O8AtZ5QmlugdMuig8xghx4pTE=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_7__.useLazyQuery\n    ];\n});\n_c = MaintenancePieChart;\nvar _c;\n$RefreshReg$(_c, \"MaintenancePieChart\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/kpis/maintance-pie-chart/maintance-pie-chart.tsx\n"));

/***/ })

});