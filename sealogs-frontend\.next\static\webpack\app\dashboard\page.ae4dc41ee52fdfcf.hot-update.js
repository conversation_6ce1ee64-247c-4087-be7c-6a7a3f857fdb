"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/ui/kpis/maintance-pie-chart/maintance-pie-chart.tsx":
/*!*********************************************************************!*\
  !*** ./src/app/ui/kpis/maintance-pie-chart/maintance-pie-chart.tsx ***!
  \*********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ MaintenancePieChart; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _components_pie_chart__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/pie-chart */ \"(app-pages-browser)/./src/components/pie-chart.tsx\");\n/* harmony import */ var _queries__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./queries */ \"(app-pages-browser)/./src/app/ui/kpis/maintance-pie-chart/queries.ts\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction MaintenancePieChart() {\n    _s();\n    const [maintenanceChecks, setMaintenanceChecks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [keywordFilter, setKeywordFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [edit_task, setEdit_task] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [overdueTasks, setOverdueTasks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [upcomingTasks, setUpcomingTasks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [extractedDays, setExtractedDays] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [under30, setUnder30] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [between30and90, setBetween30and90] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [over90, setOver90] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const init_permissions = ()=>{\n        if (permissions) {\n            if ((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_3__.hasPermission)(\"EDIT_TASK\", permissions)) {\n                setEdit_task(true);\n            } else {\n                setEdit_task(false);\n            }\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_3__.getPermissions);\n        init_permissions();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        init_permissions();\n    }, [\n        permissions\n    ]);\n    const [queryMaintenanceChecks] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_7__.useLazyQuery)(_queries__WEBPACK_IMPORTED_MODULE_5__.ReadComponentMaintenanceChecks, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Raw GraphQL Response:\", response);\n            const data = response.readComponentMaintenanceChecks.nodes;\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Extracted nodes data:\", data);\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Total maintenance checks received:\", (data === null || data === void 0 ? void 0 : data.length) || 0);\n            if (data) {\n                handleSetMaintenanceChecks(data);\n            } else {\n                console.warn(\"⚠️ [MaintenancePieChart] No data received from GraphQL query\");\n            }\n        },\n        onError: (error)=>{\n            console.error(\"❌ [MaintenancePieChart] queryMaintenanceChecks error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            // loadVessels()\n            loadMaintenanceChecks();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const loadMaintenanceChecks = async function() {\n        let searchFilter = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {\n            ...filter\n        }, searchkeywordFilter = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : keywordFilter;\n        if (searchkeywordFilter.length > 0) {\n            const promises = searchkeywordFilter.map(async (keywordFilter)=>{\n                return await queryMaintenanceChecks({\n                    variables: {\n                        filter: {\n                            ...searchFilter,\n                            ...keywordFilter\n                        }\n                    }\n                });\n            });\n            let responses = await Promise.all(promises);\n            // filter out empty results\n            responses = responses.filter((r)=>r.data.readComponentMaintenanceChecks.nodes.length > 0);\n            // flatten results\n            responses = responses.flatMap((r)=>r.data.readComponentMaintenanceChecks.nodes);\n            // filter out duplicates\n            responses = responses.filter((value, index, self)=>self.findIndex((v)=>v.id === value.id) === index);\n            handleSetMaintenanceChecks(responses);\n        } else {\n            await queryMaintenanceChecks({\n                variables: {\n                    filter: searchFilter\n                }\n            });\n        }\n    };\n    const handleSetMaintenanceChecks = (tasks)=>{\n        console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Processing tasks in handleSetMaintenanceChecks:\", tasks);\n        console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Total tasks before filtering:\", (tasks === null || tasks === void 0 ? void 0 : tasks.length) || 0);\n        const activeTasks = tasks.filter((task)=>{\n            const isActive = task.archived === false;\n            if (!isActive) {\n                console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Filtered out archived task:\", task.id);\n            }\n            return isActive;\n        }).map((task)=>{\n            const overDueInfo = (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_2__.isOverDueTask)(task);\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Task processing:\", {\n                id: task.id,\n                status: task.status,\n                expires: task.expires,\n                startDate: task.startDate,\n                isOverDueInfo: overDueInfo\n            });\n            return {\n                ...task,\n                isOverDue: overDueInfo\n            };\n        });\n        console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Active tasks after processing:\", activeTasks);\n        console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Total active tasks:\", activeTasks.length);\n        // Log status distribution\n        const statusDistribution = activeTasks.reduce((acc, task)=>{\n            var _task_isOverDue;\n            const status = ((_task_isOverDue = task.isOverDue) === null || _task_isOverDue === void 0 ? void 0 : _task_isOverDue.status) || \"Unknown\";\n            acc[status] = (acc[status] || 0) + 1;\n            return acc;\n        }, {});\n        console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Status distribution:\", statusDistribution);\n        setMaintenanceChecks(activeTasks);\n    // const appendedData: number[] = Array.from(\n    //     new Set(\n    //         activeTasks\n    //             .filter((item: any) => item.assignedToID > 0)\n    //             .map((item: any) => item.assignedToID),\n    //     ),\n    // )\n    // loadCrewMemberInfo(appendedData)\n    };\n    // const [queryCrewMemberInfo] = useLazyQuery(GET_CREW_BY_IDS, {\n    //     fetchPolicy: 'cache-and-network',\n    //     onCompleted: (response: any) => {\n    //         const data = response.readSeaLogsMembers.nodes\n    //         if (data) {\n    //             // setCrewInfo(data)\n    //         }\n    //     },\n    //     onError: (error) => {\n    //         console.error('queryCrewMemberInfo error', error)\n    //     },\n    // })\n    // const loadCrewMemberInfo = async (crewId: any) => {\n    //     await queryCrewMemberInfo({\n    //         variables: {\n    //             crewMemberIDs: crewId.length > 0 ? crewId : [0],\n    //         },\n    //     })\n    // }\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (maintenanceChecks) {\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Processing maintenanceChecks for overdue/upcoming tasks\");\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Total maintenance checks to process:\", maintenanceChecks.length);\n            // Log all task statuses and days for debugging\n            maintenanceChecks.forEach((task, index)=>{\n                var _task_isOverDue, _task_isOverDue1;\n                console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Task \".concat(index + 1, \":\"), {\n                    id: task.id,\n                    status: task.status,\n                    isOverDueStatus: (_task_isOverDue = task.isOverDue) === null || _task_isOverDue === void 0 ? void 0 : _task_isOverDue.status,\n                    isOverDueDays: (_task_isOverDue1 = task.isOverDue) === null || _task_isOverDue1 === void 0 ? void 0 : _task_isOverDue1.days,\n                    expires: task.expires\n                });\n            });\n            const overdueTasks = maintenanceChecks.filter((task)=>{\n                var _task_isOverDue, _task_isOverDue_days, _task_isOverDue1;\n                const isHighStatus = ((_task_isOverDue = task.isOverDue) === null || _task_isOverDue === void 0 ? void 0 : _task_isOverDue.status) === \"High\";\n                const hasDaysString = (_task_isOverDue1 = task.isOverDue) === null || _task_isOverDue1 === void 0 ? void 0 : (_task_isOverDue_days = _task_isOverDue1.days) === null || _task_isOverDue_days === void 0 ? void 0 : _task_isOverDue_days.includes(\"days\");\n                const isOverdue = isHighStatus && hasDaysString;\n                if (isHighStatus) {\n                    console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] High status task:\", {\n                        id: task.id,\n                        days: task.isOverDue.days,\n                        hasDaysString,\n                        isOverdue\n                    });\n                }\n                return isOverdue;\n            });\n            const upcomingTasks = maintenanceChecks.filter((task)=>{\n                var _task_isOverDue, _task_isOverDue1, _task_isOverDue2;\n                // Include tasks with 'Upcoming', 'Medium', 'Low' status AND tasks that have actual due dates\n                const hasUpcomingStatus = [\n                    \"Upcoming\",\n                    \"Medium\",\n                    \"Low\"\n                ].includes((_task_isOverDue = task.isOverDue) === null || _task_isOverDue === void 0 ? void 0 : _task_isOverDue.status);\n                // Also include tasks that have expires dates and aren't completed or high priority overdue\n                const hasValidDueDate = task.expires && ![\n                    \"Completed\",\n                    \"High\"\n                ].includes((_task_isOverDue1 = task.isOverDue) === null || _task_isOverDue1 === void 0 ? void 0 : _task_isOverDue1.status) && ((_task_isOverDue2 = task.isOverDue) === null || _task_isOverDue2 === void 0 ? void 0 : _task_isOverDue2.days) !== \"Open\";\n                const isUpcoming = hasUpcomingStatus || hasValidDueDate;\n                if (isUpcoming) {\n                    var _task_isOverDue3;\n                    console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Upcoming task:\", {\n                        id: task.id,\n                        status: (_task_isOverDue3 = task.isOverDue) === null || _task_isOverDue3 === void 0 ? void 0 : _task_isOverDue3.status,\n                        days: task.isOverDue.days,\n                        expires: task.expires,\n                        hasUpcomingStatus,\n                        hasValidDueDate\n                    });\n                }\n                return isUpcoming;\n            });\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Overdue tasks count:\", overdueTasks.length);\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Upcoming tasks count:\", upcomingTasks.length);\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Overdue tasks:\", overdueTasks);\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Upcoming tasks:\", upcomingTasks);\n            setOverdueTasks(overdueTasks.length);\n            setUpcomingTasks(upcomingTasks);\n        }\n    }, [\n        maintenanceChecks\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (upcomingTasks) {\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Processing upcomingTasks for day extraction\");\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Upcoming tasks to process:\", upcomingTasks.length);\n            const extractedDays = upcomingTasks.filter((task)=>{\n                var _task_isOverDue;\n                const daysString = ((_task_isOverDue = task.isOverDue) === null || _task_isOverDue === void 0 ? void 0 : _task_isOverDue.days) || \"\";\n                // Check if the days string contains a number (for both \"Due - X days\" and \"X days ago\" formats)\n                const hasNumericDays = /\\d+/.test(daysString) && daysString.includes(\"days\");\n                // Exclude tasks with \"Open\" status or empty days\n                const isValidDaysFormat = hasNumericDays && !daysString.includes(\"Open\");\n                // For tasks without valid day strings but with expires dates, calculate days manually\n                let hasCalculatedDays = false;\n                if (!isValidDaysFormat && task.expires) {\n                    const today = new Date();\n                    const expiresDate = new Date(task.expires);\n                    const daysDiff = Math.ceil((expiresDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n                    hasCalculatedDays = daysDiff > 0 // Only include future dates\n                    ;\n                }\n                const isValid = isValidDaysFormat || hasCalculatedDays;\n                console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Checking task for days string:\", {\n                    id: task.id,\n                    days: daysString,\n                    expires: task.expires,\n                    hasNumericDays,\n                    isValidDaysFormat,\n                    hasCalculatedDays,\n                    isValid\n                });\n                return isValid;\n            });\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Extracted days tasks count:\", extractedDays.length);\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Extracted days tasks:\", extractedDays);\n            setExtractedDays(extractedDays);\n        }\n    }, [\n        upcomingTasks\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (extractedDays) {\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Processing extractedDays for time buckets\");\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Extracted days to categorize:\", extractedDays.length);\n            const under30Tasks = extractedDays.filter((task)=>{\n                var _task_isOverDue, _task_isOverDue1;\n                // Try to extract days from the string first\n                const match = (((_task_isOverDue = task.isOverDue) === null || _task_isOverDue === void 0 ? void 0 : _task_isOverDue.days) || \"\").match(/(\\d+)/);\n                let days = match ? parseInt(match[0], 10) : null;\n                // If no days from string, calculate from expires date\n                if (days === null && task.expires) {\n                    const today = new Date();\n                    const expiresDate = new Date(task.expires);\n                    days = Math.ceil((expiresDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n                }\n                const isUnder30 = days !== null && days > 0 && days < 30;\n                console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Under 30 check:\", {\n                    id: task.id,\n                    daysString: (_task_isOverDue1 = task.isOverDue) === null || _task_isOverDue1 === void 0 ? void 0 : _task_isOverDue1.days,\n                    expires: task.expires,\n                    extractedDays: days,\n                    isUnder30\n                });\n                return isUnder30;\n            });\n            const between30and90Tasks = extractedDays.filter((task)=>{\n                var _task_isOverDue, _task_isOverDue1;\n                // Try to extract days from the string first\n                const match = (((_task_isOverDue = task.isOverDue) === null || _task_isOverDue === void 0 ? void 0 : _task_isOverDue.days) || \"\").match(/(\\d+)/);\n                let days = match ? parseInt(match[0], 10) : null;\n                // If no days from string, calculate from expires date\n                if (days === null && task.expires) {\n                    const today = new Date();\n                    const expiresDate = new Date(task.expires);\n                    days = Math.ceil((expiresDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n                }\n                const isBetween = days !== null && days >= 30 && days <= 90;\n                console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] 30-90 check:\", {\n                    id: task.id,\n                    daysString: (_task_isOverDue1 = task.isOverDue) === null || _task_isOverDue1 === void 0 ? void 0 : _task_isOverDue1.days,\n                    expires: task.expires,\n                    extractedDays: days,\n                    isBetween\n                });\n                return isBetween;\n            });\n            const over90Tasks = extractedDays.filter((task)=>{\n                var _task_isOverDue, _task_isOverDue1;\n                // Try to extract days from the string first\n                const match = (((_task_isOverDue = task.isOverDue) === null || _task_isOverDue === void 0 ? void 0 : _task_isOverDue.days) || \"\").match(/(\\d+)/);\n                let days = match ? parseInt(match[0], 10) : null;\n                // If no days from string, calculate from expires date\n                if (days === null && task.expires) {\n                    const today = new Date();\n                    const expiresDate = new Date(task.expires);\n                    days = Math.ceil((expiresDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n                }\n                const isOver90 = days !== null && days > 90;\n                console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Over 90 check:\", {\n                    id: task.id,\n                    daysString: (_task_isOverDue1 = task.isOverDue) === null || _task_isOverDue1 === void 0 ? void 0 : _task_isOverDue1.days,\n                    expires: task.expires,\n                    extractedDays: days,\n                    isOver90\n                });\n                return isOver90;\n            });\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Time bucket results:\");\n            console.log(\"  - Under 30 days:\", under30Tasks.length);\n            console.log(\"  - 30-90 days:\", between30and90Tasks.length);\n            console.log(\"  - Over 90 days:\", over90Tasks.length);\n            setUnder30(under30Tasks.length);\n            setBetween30and90(between30and90Tasks.length);\n            setOver90(over90Tasks.length);\n        }\n    }, [\n        extractedDays\n    ]);\n    const chartData = [\n        {\n            title: \"Tasks overdue\",\n            amount: overdueTasks || 0,\n            fill: \"var(--color-overdue)\",\n            stroke: \"hsl(1, 97%, 60%)\"\n        },\n        {\n            title: \"Tasks due < 30\",\n            amount: under30 || 0,\n            fill: \"var(--color-thirtyDays)\",\n            stroke: \"hsl(205, 78%, 48%)\"\n        },\n        {\n            title: \"Tasks due 30 - 90\",\n            amount: between30and90 || 0,\n            fill: \"var(--color-thirtyToNinety)\",\n            stroke: \"hsl(205, 32%, 45%)\"\n        },\n        {\n            title: \"Tasks due > 90\",\n            amount: over90 || 0,\n            fill: \"var(--color-ninetyPlus)\",\n            stroke: \"hsl(174, 100%, 40%)\"\n        },\n        {\n            title: \"Open tasks\",\n            amount: openTasks || 0,\n            fill: \"var(--color-open)\",\n            stroke: \"hsl(220, 14%, 60%)\"\n        }\n    ];\n    // Log final chart data before rendering\n    console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Final chart data:\", chartData);\n    console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Chart data summary:\", {\n        overdue: overdueTasks || 0,\n        under30: under30 || 0,\n        between30and90: between30and90 || 0,\n        over90: over90 || 0,\n        open: openTasks || 0,\n        total: (overdueTasks || 0) + (under30 || 0) + (between30and90 || 0) + (over90 || 0) + (openTasks || 0)\n    });\n    const chartConfig = {\n        amount: {\n            label: \"Amount\"\n        },\n        overdue: {\n            label: \"Tasks overdue\",\n            color: \"var(--chart-1)\"\n        },\n        thirtyDays: {\n            label: \"Tasks due under 30-days\",\n            color: \"var(--chart-3)\"\n        },\n        thirtyToNinety: {\n            label: \"Tasks due 30-90 days\",\n            color: \"var(--chart-4)\"\n        },\n        ninetyPlus: {\n            label: \"Tasks due 90+ days\",\n            color: \"var(--chart-5)\"\n        },\n        open: {\n            label: \"Open tasks (no due date)\",\n            color: \"var(--chart-6)\"\n        }\n    };\n    // Log render information\n    console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Component rendering with:\", {\n        isLoading,\n        hasMaintenanceChecks: !!maintenanceChecks,\n        maintenanceChecksCount: (maintenanceChecks === null || maintenanceChecks === void 0 ? void 0 : maintenanceChecks.length) || 0,\n        chartDataValid: chartData.every((item)=>typeof item.amount === \"number\"),\n        chartDataHasValues: chartData.some((item)=>item.amount > 0)\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col text-center\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.P, {\n                children: \"Tasks due - days\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\kpis\\\\maintance-pie-chart\\\\maintance-pie-chart.tsx\",\n                lineNumber: 551,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_pie_chart__WEBPACK_IMPORTED_MODULE_4__.PieChartComponent, {\n                chartData: chartData,\n                chartConfig: chartConfig\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\kpis\\\\maintance-pie-chart\\\\maintance-pie-chart.tsx\",\n                lineNumber: 552,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\kpis\\\\maintance-pie-chart\\\\maintance-pie-chart.tsx\",\n        lineNumber: 550,\n        columnNumber: 9\n    }, this);\n}\n_s(MaintenancePieChart, \"M4O8AtZ5QmlugdMuig8xghx4pTE=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_7__.useLazyQuery\n    ];\n});\n_c = MaintenancePieChart;\nvar _c;\n$RefreshReg$(_c, \"MaintenancePieChart\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/kpis/maintance-pie-chart/maintance-pie-chart.tsx\n"));

/***/ })

});