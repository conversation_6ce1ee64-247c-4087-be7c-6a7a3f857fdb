"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/ui/kpis/maintance-pie-chart/maintance-pie-chart.tsx":
/*!*********************************************************************!*\
  !*** ./src/app/ui/kpis/maintance-pie-chart/maintance-pie-chart.tsx ***!
  \*********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ MaintenancePieChart; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _components_pie_chart__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/pie-chart */ \"(app-pages-browser)/./src/components/pie-chart.tsx\");\n/* harmony import */ var _queries__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./queries */ \"(app-pages-browser)/./src/app/ui/kpis/maintance-pie-chart/queries.ts\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction MaintenancePieChart() {\n    _s();\n    const [maintenanceChecks, setMaintenanceChecks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [keywordFilter, setKeywordFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [edit_task, setEdit_task] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [overdueTasks, setOverdueTasks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [upcomingTasks, setUpcomingTasks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [extractedDays, setExtractedDays] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [under30, setUnder30] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [between30and90, setBetween30and90] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [over90, setOver90] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [openTasks, setOpenTasks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const init_permissions = ()=>{\n        if (permissions) {\n            if ((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_3__.hasPermission)(\"EDIT_TASK\", permissions)) {\n                setEdit_task(true);\n            } else {\n                setEdit_task(false);\n            }\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_3__.getPermissions);\n        init_permissions();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        init_permissions();\n    }, [\n        permissions\n    ]);\n    const [queryMaintenanceChecks] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_7__.useLazyQuery)(_queries__WEBPACK_IMPORTED_MODULE_5__.ReadComponentMaintenanceChecks, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Raw GraphQL Response:\", response);\n            const data = response.readComponentMaintenanceChecks.nodes;\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Extracted nodes data:\", data);\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Total maintenance checks received:\", (data === null || data === void 0 ? void 0 : data.length) || 0);\n            if (data) {\n                handleSetMaintenanceChecks(data);\n            } else {\n                console.warn(\"⚠️ [MaintenancePieChart] No data received from GraphQL query\");\n            }\n        },\n        onError: (error)=>{\n            console.error(\"❌ [MaintenancePieChart] queryMaintenanceChecks error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            // loadVessels()\n            loadMaintenanceChecks();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const loadMaintenanceChecks = async function() {\n        let searchFilter = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {\n            ...filter\n        }, searchkeywordFilter = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : keywordFilter;\n        if (searchkeywordFilter.length > 0) {\n            const promises = searchkeywordFilter.map(async (keywordFilter)=>{\n                return await queryMaintenanceChecks({\n                    variables: {\n                        filter: {\n                            ...searchFilter,\n                            ...keywordFilter\n                        }\n                    }\n                });\n            });\n            let responses = await Promise.all(promises);\n            // filter out empty results\n            responses = responses.filter((r)=>r.data.readComponentMaintenanceChecks.nodes.length > 0);\n            // flatten results\n            responses = responses.flatMap((r)=>r.data.readComponentMaintenanceChecks.nodes);\n            // filter out duplicates\n            responses = responses.filter((value, index, self)=>self.findIndex((v)=>v.id === value.id) === index);\n            handleSetMaintenanceChecks(responses);\n        } else {\n            await queryMaintenanceChecks({\n                variables: {\n                    filter: searchFilter\n                }\n            });\n        }\n    };\n    const handleSetMaintenanceChecks = (tasks)=>{\n        console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Processing tasks in handleSetMaintenanceChecks:\", tasks);\n        console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Total tasks before filtering:\", (tasks === null || tasks === void 0 ? void 0 : tasks.length) || 0);\n        const activeTasks = tasks.filter((task)=>{\n            const isActive = task.archived === false;\n            if (!isActive) {\n                console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Filtered out archived task:\", task.id);\n            }\n            return isActive;\n        }).map((task)=>{\n            const overDueInfo = (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_2__.isOverDueTask)(task);\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Task processing:\", {\n                id: task.id,\n                status: task.status,\n                expires: task.expires,\n                startDate: task.startDate,\n                isOverDueInfo: overDueInfo\n            });\n            return {\n                ...task,\n                isOverDue: overDueInfo\n            };\n        });\n        console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Active tasks after processing:\", activeTasks);\n        console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Total active tasks:\", activeTasks.length);\n        // Log status distribution\n        const statusDistribution = activeTasks.reduce((acc, task)=>{\n            var _task_isOverDue;\n            const status = ((_task_isOverDue = task.isOverDue) === null || _task_isOverDue === void 0 ? void 0 : _task_isOverDue.status) || \"Unknown\";\n            acc[status] = (acc[status] || 0) + 1;\n            return acc;\n        }, {});\n        console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Status distribution:\", statusDistribution);\n        setMaintenanceChecks(activeTasks);\n    // const appendedData: number[] = Array.from(\n    //     new Set(\n    //         activeTasks\n    //             .filter((item: any) => item.assignedToID > 0)\n    //             .map((item: any) => item.assignedToID),\n    //     ),\n    // )\n    // loadCrewMemberInfo(appendedData)\n    };\n    // const [queryCrewMemberInfo] = useLazyQuery(GET_CREW_BY_IDS, {\n    //     fetchPolicy: 'cache-and-network',\n    //     onCompleted: (response: any) => {\n    //         const data = response.readSeaLogsMembers.nodes\n    //         if (data) {\n    //             // setCrewInfo(data)\n    //         }\n    //     },\n    //     onError: (error) => {\n    //         console.error('queryCrewMemberInfo error', error)\n    //     },\n    // })\n    // const loadCrewMemberInfo = async (crewId: any) => {\n    //     await queryCrewMemberInfo({\n    //         variables: {\n    //             crewMemberIDs: crewId.length > 0 ? crewId : [0],\n    //         },\n    //     })\n    // }\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (maintenanceChecks) {\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Processing maintenanceChecks for overdue/upcoming tasks\");\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Total maintenance checks to process:\", maintenanceChecks.length);\n            // Log all task statuses and days for debugging\n            maintenanceChecks.forEach((task, index)=>{\n                var _task_isOverDue, _task_isOverDue1;\n                console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Task \".concat(index + 1, \":\"), {\n                    id: task.id,\n                    status: task.status,\n                    isOverDueStatus: (_task_isOverDue = task.isOverDue) === null || _task_isOverDue === void 0 ? void 0 : _task_isOverDue.status,\n                    isOverDueDays: (_task_isOverDue1 = task.isOverDue) === null || _task_isOverDue1 === void 0 ? void 0 : _task_isOverDue1.days,\n                    expires: task.expires\n                });\n            });\n            const overdueTasks = maintenanceChecks.filter((task)=>{\n                var _task_isOverDue, _task_isOverDue_days, _task_isOverDue1;\n                const isHighStatus = ((_task_isOverDue = task.isOverDue) === null || _task_isOverDue === void 0 ? void 0 : _task_isOverDue.status) === \"High\";\n                const hasDaysString = (_task_isOverDue1 = task.isOverDue) === null || _task_isOverDue1 === void 0 ? void 0 : (_task_isOverDue_days = _task_isOverDue1.days) === null || _task_isOverDue_days === void 0 ? void 0 : _task_isOverDue_days.includes(\"days\");\n                const isOverdue = isHighStatus && hasDaysString;\n                if (isHighStatus) {\n                    console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] High status task:\", {\n                        id: task.id,\n                        days: task.isOverDue.days,\n                        hasDaysString,\n                        isOverdue\n                    });\n                }\n                return isOverdue;\n            });\n            const upcomingTasks = maintenanceChecks.filter((task)=>{\n                var _task_isOverDue, _task_isOverDue1, _task_isOverDue2;\n                // Include tasks with 'Upcoming', 'Medium', 'Low' status AND tasks that have actual due dates\n                const hasUpcomingStatus = [\n                    \"Upcoming\",\n                    \"Medium\",\n                    \"Low\"\n                ].includes((_task_isOverDue = task.isOverDue) === null || _task_isOverDue === void 0 ? void 0 : _task_isOverDue.status);\n                // Also include tasks that have expires dates and aren't completed or high priority overdue\n                const hasValidDueDate = task.expires && ![\n                    \"Completed\",\n                    \"High\"\n                ].includes((_task_isOverDue1 = task.isOverDue) === null || _task_isOverDue1 === void 0 ? void 0 : _task_isOverDue1.status) && ((_task_isOverDue2 = task.isOverDue) === null || _task_isOverDue2 === void 0 ? void 0 : _task_isOverDue2.days) !== \"Open\";\n                const isUpcoming = hasUpcomingStatus || hasValidDueDate;\n                if (isUpcoming) {\n                    var _task_isOverDue3;\n                    console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Upcoming task:\", {\n                        id: task.id,\n                        status: (_task_isOverDue3 = task.isOverDue) === null || _task_isOverDue3 === void 0 ? void 0 : _task_isOverDue3.status,\n                        days: task.isOverDue.days,\n                        expires: task.expires,\n                        hasUpcomingStatus,\n                        hasValidDueDate\n                    });\n                }\n                return isUpcoming;\n            });\n            // Count open tasks (tasks with no specific due dates)\n            const openTasksCount = maintenanceChecks.filter((task)=>{\n                var _task_isOverDue, _task_isOverDue1;\n                const isOpen = ((_task_isOverDue = task.isOverDue) === null || _task_isOverDue === void 0 ? void 0 : _task_isOverDue.days) === \"Open\" && ![\n                    \"High\",\n                    \"Completed\"\n                ].includes((_task_isOverDue1 = task.isOverDue) === null || _task_isOverDue1 === void 0 ? void 0 : _task_isOverDue1.status);\n                if (isOpen) {\n                    var _task_isOverDue2;\n                    console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Open task:\", {\n                        id: task.id,\n                        status: (_task_isOverDue2 = task.isOverDue) === null || _task_isOverDue2 === void 0 ? void 0 : _task_isOverDue2.status,\n                        days: task.isOverDue.days\n                    });\n                }\n                return isOpen;\n            });\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Overdue tasks count:\", overdueTasks.length);\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Upcoming tasks count:\", upcomingTasks.length);\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Open tasks count:\", openTasksCount.length);\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Overdue tasks:\", overdueTasks);\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Upcoming tasks:\", upcomingTasks);\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Open tasks:\", openTasksCount);\n            setOverdueTasks(overdueTasks.length);\n            setUpcomingTasks(upcomingTasks);\n            setOpenTasks(openTasksCount.length);\n        }\n    }, [\n        maintenanceChecks\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (upcomingTasks) {\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Processing upcomingTasks for day extraction\");\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Upcoming tasks to process:\", upcomingTasks.length);\n            const extractedDays = upcomingTasks.filter((task)=>{\n                var _task_isOverDue;\n                const daysString = ((_task_isOverDue = task.isOverDue) === null || _task_isOverDue === void 0 ? void 0 : _task_isOverDue.days) || \"\";\n                // Check if the days string contains a number (for both \"Due - X days\" and \"X days ago\" formats)\n                const hasNumericDays = /\\d+/.test(daysString) && daysString.includes(\"days\");\n                // Exclude tasks with \"Open\" status or empty days\n                const isValidDaysFormat = hasNumericDays && !daysString.includes(\"Open\");\n                // For tasks without valid day strings but with expires dates, calculate days manually\n                let hasCalculatedDays = false;\n                if (!isValidDaysFormat && task.expires) {\n                    const today = new Date();\n                    const expiresDate = new Date(task.expires);\n                    const daysDiff = Math.ceil((expiresDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n                    hasCalculatedDays = daysDiff > 0 // Only include future dates\n                    ;\n                }\n                const isValid = isValidDaysFormat || hasCalculatedDays;\n                console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Checking task for days string:\", {\n                    id: task.id,\n                    days: daysString,\n                    expires: task.expires,\n                    hasNumericDays,\n                    isValidDaysFormat,\n                    hasCalculatedDays,\n                    isValid\n                });\n                return isValid;\n            });\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Extracted days tasks count:\", extractedDays.length);\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Extracted days tasks:\", extractedDays);\n            setExtractedDays(extractedDays);\n        }\n    }, [\n        upcomingTasks\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (extractedDays) {\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Processing extractedDays for time buckets\");\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Extracted days to categorize:\", extractedDays.length);\n            const under30Tasks = extractedDays.filter((task)=>{\n                var _task_isOverDue, _task_isOverDue1;\n                // Try to extract days from the string first\n                const match = (((_task_isOverDue = task.isOverDue) === null || _task_isOverDue === void 0 ? void 0 : _task_isOverDue.days) || \"\").match(/(\\d+)/);\n                let days = match ? parseInt(match[0], 10) : null;\n                // If no days from string, calculate from expires date\n                if (days === null && task.expires) {\n                    const today = new Date();\n                    const expiresDate = new Date(task.expires);\n                    days = Math.ceil((expiresDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n                }\n                const isUnder30 = days !== null && days > 0 && days < 30;\n                console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Under 30 check:\", {\n                    id: task.id,\n                    daysString: (_task_isOverDue1 = task.isOverDue) === null || _task_isOverDue1 === void 0 ? void 0 : _task_isOverDue1.days,\n                    expires: task.expires,\n                    extractedDays: days,\n                    isUnder30\n                });\n                return isUnder30;\n            });\n            const between30and90Tasks = extractedDays.filter((task)=>{\n                var _task_isOverDue, _task_isOverDue1;\n                // Try to extract days from the string first\n                const match = (((_task_isOverDue = task.isOverDue) === null || _task_isOverDue === void 0 ? void 0 : _task_isOverDue.days) || \"\").match(/(\\d+)/);\n                let days = match ? parseInt(match[0], 10) : null;\n                // If no days from string, calculate from expires date\n                if (days === null && task.expires) {\n                    const today = new Date();\n                    const expiresDate = new Date(task.expires);\n                    days = Math.ceil((expiresDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n                }\n                const isBetween = days !== null && days >= 30 && days <= 90;\n                console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] 30-90 check:\", {\n                    id: task.id,\n                    daysString: (_task_isOverDue1 = task.isOverDue) === null || _task_isOverDue1 === void 0 ? void 0 : _task_isOverDue1.days,\n                    expires: task.expires,\n                    extractedDays: days,\n                    isBetween\n                });\n                return isBetween;\n            });\n            const over90Tasks = extractedDays.filter((task)=>{\n                var _task_isOverDue, _task_isOverDue1;\n                // Try to extract days from the string first\n                const match = (((_task_isOverDue = task.isOverDue) === null || _task_isOverDue === void 0 ? void 0 : _task_isOverDue.days) || \"\").match(/(\\d+)/);\n                let days = match ? parseInt(match[0], 10) : null;\n                // If no days from string, calculate from expires date\n                if (days === null && task.expires) {\n                    const today = new Date();\n                    const expiresDate = new Date(task.expires);\n                    days = Math.ceil((expiresDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n                }\n                const isOver90 = days !== null && days > 90;\n                console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Over 90 check:\", {\n                    id: task.id,\n                    daysString: (_task_isOverDue1 = task.isOverDue) === null || _task_isOverDue1 === void 0 ? void 0 : _task_isOverDue1.days,\n                    expires: task.expires,\n                    extractedDays: days,\n                    isOver90\n                });\n                return isOver90;\n            });\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Time bucket results:\");\n            console.log(\"  - Under 30 days:\", under30Tasks.length);\n            console.log(\"  - 30-90 days:\", between30and90Tasks.length);\n            console.log(\"  - Over 90 days:\", over90Tasks.length);\n            setUnder30(under30Tasks.length);\n            setBetween30and90(between30and90Tasks.length);\n            setOver90(over90Tasks.length);\n        }\n    }, [\n        extractedDays\n    ]);\n    const chartData = [\n        {\n            title: \"Tasks overdue\",\n            amount: overdueTasks || 0,\n            fill: \"var(--color-overdue)\",\n            stroke: \"hsl(1, 97%, 60%)\"\n        },\n        {\n            title: \"Tasks due < 30\",\n            amount: under30 || 0,\n            fill: \"var(--color-thirtyDays)\",\n            stroke: \"hsl(205, 78%, 48%)\"\n        },\n        {\n            title: \"Tasks due 30 - 90\",\n            amount: between30and90 || 0,\n            fill: \"var(--color-thirtyToNinety)\",\n            stroke: \"hsl(205, 32%, 45%)\"\n        },\n        {\n            title: \"Tasks due > 90\",\n            amount: over90 || 0,\n            fill: \"var(--color-ninetyPlus)\",\n            stroke: \"hsl(174, 100%, 40%)\"\n        },\n        {\n            title: \"Open tasks\",\n            amount: openTasks || 0,\n            fill: \"var(--color-open)\",\n            stroke: \"hsl(220, 14%, 60%)\"\n        }\n    ];\n    // Log final chart data before rendering\n    console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Final chart data:\", chartData);\n    console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Chart data summary:\", {\n        overdue: overdueTasks || 0,\n        under30: under30 || 0,\n        between30and90: between30and90 || 0,\n        over90: over90 || 0,\n        open: openTasks || 0,\n        total: (overdueTasks || 0) + (under30 || 0) + (between30and90 || 0) + (over90 || 0) + (openTasks || 0)\n    });\n    const chartConfig = {\n        amount: {\n            label: \"Amount\"\n        },\n        overdue: {\n            label: \"Tasks overdue\",\n            color: \"var(--chart-1)\"\n        },\n        thirtyDays: {\n            label: \"Tasks due under 30-days\",\n            color: \"var(--chart-3)\"\n        },\n        thirtyToNinety: {\n            label: \"Tasks due 30-90 days\",\n            color: \"var(--chart-4)\"\n        },\n        ninetyPlus: {\n            label: \"Tasks due 90+ days\",\n            color: \"var(--chart-5)\"\n        },\n        open: {\n            label: \"Open tasks (no due date)\",\n            color: \"var(--chart-6)\"\n        }\n    };\n    // Log render information\n    console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Component rendering with:\", {\n        isLoading,\n        hasMaintenanceChecks: !!maintenanceChecks,\n        maintenanceChecksCount: (maintenanceChecks === null || maintenanceChecks === void 0 ? void 0 : maintenanceChecks.length) || 0,\n        chartDataValid: chartData.every((item)=>typeof item.amount === \"number\"),\n        chartDataHasValues: chartData.some((item)=>item.amount > 0)\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col text-center\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.P, {\n                children: \"Tasks due - days\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\kpis\\\\maintance-pie-chart\\\\maintance-pie-chart.tsx\",\n                lineNumber: 573,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_pie_chart__WEBPACK_IMPORTED_MODULE_4__.PieChartComponent, {\n                chartData: chartData,\n                chartConfig: chartConfig\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\kpis\\\\maintance-pie-chart\\\\maintance-pie-chart.tsx\",\n                lineNumber: 574,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\kpis\\\\maintance-pie-chart\\\\maintance-pie-chart.tsx\",\n        lineNumber: 572,\n        columnNumber: 9\n    }, this);\n}\n_s(MaintenancePieChart, \"R5rGRBaiacgi5V4zjdZJmd+4i2Q=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_7__.useLazyQuery\n    ];\n});\n_c = MaintenancePieChart;\nvar _c;\n$RefreshReg$(_c, \"MaintenancePieChart\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/kpis/maintance-pie-chart/maintance-pie-chart.tsx\n"));

/***/ })

});