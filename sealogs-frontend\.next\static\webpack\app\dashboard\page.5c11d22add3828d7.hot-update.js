"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/ui/kpis/maintance-pie-chart/maintance-pie-chart.tsx":
/*!*********************************************************************!*\
  !*** ./src/app/ui/kpis/maintance-pie-chart/maintance-pie-chart.tsx ***!
  \*********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ MaintenancePieChart; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _components_pie_chart__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/pie-chart */ \"(app-pages-browser)/./src/components/pie-chart.tsx\");\n/* harmony import */ var _queries__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./queries */ \"(app-pages-browser)/./src/app/ui/kpis/maintance-pie-chart/queries.ts\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction MaintenancePieChart() {\n    _s();\n    const [maintenanceChecks, setMaintenanceChecks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [keywordFilter, setKeywordFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [edit_task, setEdit_task] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [overdueTasks, setOverdueTasks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [upcomingTasks, setUpcomingTasks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [extractedDays, setExtractedDays] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [under30, setUnder30] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [between30and90, setBetween30and90] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [over90, setOver90] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const init_permissions = ()=>{\n        if (permissions) {\n            if ((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_3__.hasPermission)(\"EDIT_TASK\", permissions)) {\n                setEdit_task(true);\n            } else {\n                setEdit_task(false);\n            }\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_3__.getPermissions);\n        init_permissions();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        init_permissions();\n    }, [\n        permissions\n    ]);\n    const [queryMaintenanceChecks] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_7__.useLazyQuery)(_queries__WEBPACK_IMPORTED_MODULE_5__.ReadComponentMaintenanceChecks, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Raw GraphQL Response:\", response);\n            const data = response.readComponentMaintenanceChecks.nodes;\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Extracted nodes data:\", data);\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Total maintenance checks received:\", (data === null || data === void 0 ? void 0 : data.length) || 0);\n            if (data) {\n                handleSetMaintenanceChecks(data);\n            } else {\n                console.warn(\"⚠️ [MaintenancePieChart] No data received from GraphQL query\");\n            }\n        },\n        onError: (error)=>{\n            console.error(\"❌ [MaintenancePieChart] queryMaintenanceChecks error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            // loadVessels()\n            loadMaintenanceChecks();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const loadMaintenanceChecks = async function() {\n        let searchFilter = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {\n            ...filter\n        }, searchkeywordFilter = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : keywordFilter;\n        if (searchkeywordFilter.length > 0) {\n            const promises = searchkeywordFilter.map(async (keywordFilter)=>{\n                return await queryMaintenanceChecks({\n                    variables: {\n                        filter: {\n                            ...searchFilter,\n                            ...keywordFilter\n                        }\n                    }\n                });\n            });\n            let responses = await Promise.all(promises);\n            // filter out empty results\n            responses = responses.filter((r)=>r.data.readComponentMaintenanceChecks.nodes.length > 0);\n            // flatten results\n            responses = responses.flatMap((r)=>r.data.readComponentMaintenanceChecks.nodes);\n            // filter out duplicates\n            responses = responses.filter((value, index, self)=>self.findIndex((v)=>v.id === value.id) === index);\n            handleSetMaintenanceChecks(responses);\n        } else {\n            await queryMaintenanceChecks({\n                variables: {\n                    filter: searchFilter\n                }\n            });\n        }\n    };\n    const handleSetMaintenanceChecks = (tasks)=>{\n        console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Processing tasks in handleSetMaintenanceChecks:\", tasks);\n        console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Total tasks before filtering:\", (tasks === null || tasks === void 0 ? void 0 : tasks.length) || 0);\n        const activeTasks = tasks.filter((task)=>{\n            const isActive = task.archived === false;\n            if (!isActive) {\n                console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Filtered out archived task:\", task.id);\n            }\n            return isActive;\n        }).map((task)=>{\n            const overDueInfo = (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_2__.isOverDueTask)(task);\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Task processing:\", {\n                id: task.id,\n                status: task.status,\n                expires: task.expires,\n                startDate: task.startDate,\n                isOverDueInfo: overDueInfo\n            });\n            return {\n                ...task,\n                isOverDue: overDueInfo\n            };\n        });\n        console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Active tasks after processing:\", activeTasks);\n        console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Total active tasks:\", activeTasks.length);\n        // Log status distribution\n        const statusDistribution = activeTasks.reduce((acc, task)=>{\n            var _task_isOverDue;\n            const status = ((_task_isOverDue = task.isOverDue) === null || _task_isOverDue === void 0 ? void 0 : _task_isOverDue.status) || \"Unknown\";\n            acc[status] = (acc[status] || 0) + 1;\n            return acc;\n        }, {});\n        console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Status distribution:\", statusDistribution);\n        setMaintenanceChecks(activeTasks);\n    // const appendedData: number[] = Array.from(\n    //     new Set(\n    //         activeTasks\n    //             .filter((item: any) => item.assignedToID > 0)\n    //             .map((item: any) => item.assignedToID),\n    //     ),\n    // )\n    // loadCrewMemberInfo(appendedData)\n    };\n    // const [queryCrewMemberInfo] = useLazyQuery(GET_CREW_BY_IDS, {\n    //     fetchPolicy: 'cache-and-network',\n    //     onCompleted: (response: any) => {\n    //         const data = response.readSeaLogsMembers.nodes\n    //         if (data) {\n    //             // setCrewInfo(data)\n    //         }\n    //     },\n    //     onError: (error) => {\n    //         console.error('queryCrewMemberInfo error', error)\n    //     },\n    // })\n    // const loadCrewMemberInfo = async (crewId: any) => {\n    //     await queryCrewMemberInfo({\n    //         variables: {\n    //             crewMemberIDs: crewId.length > 0 ? crewId : [0],\n    //         },\n    //     })\n    // }\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (maintenanceChecks) {\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Processing maintenanceChecks for overdue/upcoming tasks\");\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Total maintenance checks to process:\", maintenanceChecks.length);\n            // Log all task statuses and days for debugging\n            maintenanceChecks.forEach((task, index)=>{\n                var _task_isOverDue, _task_isOverDue1;\n                console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Task \".concat(index + 1, \":\"), {\n                    id: task.id,\n                    status: task.status,\n                    isOverDueStatus: (_task_isOverDue = task.isOverDue) === null || _task_isOverDue === void 0 ? void 0 : _task_isOverDue.status,\n                    isOverDueDays: (_task_isOverDue1 = task.isOverDue) === null || _task_isOverDue1 === void 0 ? void 0 : _task_isOverDue1.days,\n                    expires: task.expires\n                });\n            });\n            const overdueTasks = maintenanceChecks.filter((task)=>{\n                var _task_isOverDue, _task_isOverDue_days, _task_isOverDue1;\n                const isHighStatus = ((_task_isOverDue = task.isOverDue) === null || _task_isOverDue === void 0 ? void 0 : _task_isOverDue.status) === \"High\";\n                const hasDaysString = (_task_isOverDue1 = task.isOverDue) === null || _task_isOverDue1 === void 0 ? void 0 : (_task_isOverDue_days = _task_isOverDue1.days) === null || _task_isOverDue_days === void 0 ? void 0 : _task_isOverDue_days.includes(\"days\");\n                const isOverdue = isHighStatus && hasDaysString;\n                if (isHighStatus) {\n                    console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] High status task:\", {\n                        id: task.id,\n                        days: task.isOverDue.days,\n                        hasDaysString,\n                        isOverdue\n                    });\n                }\n                return isOverdue;\n            });\n            const upcomingTasks = maintenanceChecks.filter((task)=>{\n                var _task_isOverDue, _task_isOverDue1, _task_isOverDue2;\n                // Include tasks with 'Upcoming', 'Medium', 'Low' status AND tasks that have actual due dates\n                const hasUpcomingStatus = [\n                    \"Upcoming\",\n                    \"Medium\",\n                    \"Low\"\n                ].includes((_task_isOverDue = task.isOverDue) === null || _task_isOverDue === void 0 ? void 0 : _task_isOverDue.status);\n                // Also include tasks that have expires dates and aren't completed or high priority overdue\n                const hasValidDueDate = task.expires && ![\n                    \"Completed\",\n                    \"High\"\n                ].includes((_task_isOverDue1 = task.isOverDue) === null || _task_isOverDue1 === void 0 ? void 0 : _task_isOverDue1.status) && ((_task_isOverDue2 = task.isOverDue) === null || _task_isOverDue2 === void 0 ? void 0 : _task_isOverDue2.days) !== \"Open\";\n                const isUpcoming = hasUpcomingStatus || hasValidDueDate;\n                if (isUpcoming) {\n                    var _task_isOverDue3;\n                    console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Upcoming task:\", {\n                        id: task.id,\n                        status: (_task_isOverDue3 = task.isOverDue) === null || _task_isOverDue3 === void 0 ? void 0 : _task_isOverDue3.status,\n                        days: task.isOverDue.days,\n                        expires: task.expires,\n                        hasUpcomingStatus,\n                        hasValidDueDate\n                    });\n                }\n                return isUpcoming;\n            });\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Overdue tasks count:\", overdueTasks.length);\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Upcoming tasks count:\", upcomingTasks.length);\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Overdue tasks:\", overdueTasks);\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Upcoming tasks:\", upcomingTasks);\n            setOverdueTasks(overdueTasks.length);\n            setUpcomingTasks(upcomingTasks);\n        }\n    }, [\n        maintenanceChecks\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (upcomingTasks) {\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Processing upcomingTasks for day extraction\");\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Upcoming tasks to process:\", upcomingTasks.length);\n            const extractedDays = upcomingTasks.filter((task)=>{\n                var _task_isOverDue;\n                const daysString = ((_task_isOverDue = task.isOverDue) === null || _task_isOverDue === void 0 ? void 0 : _task_isOverDue.days) || \"\";\n                // Check if the days string contains a number (for both \"Due - X days\" and \"X days ago\" formats)\n                const hasNumericDays = /\\d+/.test(daysString) && daysString.includes(\"days\");\n                // Exclude tasks with \"Open\" status or empty days\n                const isValidDaysFormat = hasNumericDays && !daysString.includes(\"Open\");\n                // For tasks without valid day strings but with expires dates, calculate days manually\n                let hasCalculatedDays = false;\n                if (!isValidDaysFormat && task.expires) {\n                    const today = new Date();\n                    const expiresDate = new Date(task.expires);\n                    const daysDiff = Math.ceil((expiresDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n                    hasCalculatedDays = daysDiff > 0 // Only include future dates\n                    ;\n                }\n                const isValid = isValidDaysFormat || hasCalculatedDays;\n                console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Checking task for days string:\", {\n                    id: task.id,\n                    days: daysString,\n                    expires: task.expires,\n                    hasNumericDays,\n                    isValidDaysFormat,\n                    hasCalculatedDays,\n                    isValid\n                });\n                return isValid;\n            });\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Extracted days tasks count:\", extractedDays.length);\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Extracted days tasks:\", extractedDays);\n            setExtractedDays(extractedDays);\n        }\n    }, [\n        upcomingTasks\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (extractedDays) {\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Processing extractedDays for time buckets\");\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Extracted days to categorize:\", extractedDays.length);\n            const under30Tasks = extractedDays.filter((task)=>{\n                var _task_isOverDue, _task_isOverDue1;\n                // Try to extract days from the string first\n                const match = (((_task_isOverDue = task.isOverDue) === null || _task_isOverDue === void 0 ? void 0 : _task_isOverDue.days) || \"\").match(/(\\d+)/);\n                let days = match ? parseInt(match[0], 10) : null;\n                // If no days from string, calculate from expires date\n                if (days === null && task.expires) {\n                    const today = new Date();\n                    const expiresDate = new Date(task.expires);\n                    days = Math.ceil((expiresDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n                }\n                const isUnder30 = days !== null && days > 0 && days < 30;\n                console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Under 30 check:\", {\n                    id: task.id,\n                    daysString: (_task_isOverDue1 = task.isOverDue) === null || _task_isOverDue1 === void 0 ? void 0 : _task_isOverDue1.days,\n                    expires: task.expires,\n                    extractedDays: days,\n                    isUnder30\n                });\n                return isUnder30;\n            });\n            const between30and90Tasks = extractedDays.filter((task)=>{\n                var _task_isOverDue, _task_isOverDue1;\n                // Try to extract days from the string first\n                const match = (((_task_isOverDue = task.isOverDue) === null || _task_isOverDue === void 0 ? void 0 : _task_isOverDue.days) || \"\").match(/(\\d+)/);\n                let days = match ? parseInt(match[0], 10) : null;\n                // If no days from string, calculate from expires date\n                if (days === null && task.expires) {\n                    const today = new Date();\n                    const expiresDate = new Date(task.expires);\n                    days = Math.ceil((expiresDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n                }\n                const isBetween = days !== null && days >= 30 && days <= 90;\n                console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] 30-90 check:\", {\n                    id: task.id,\n                    daysString: (_task_isOverDue1 = task.isOverDue) === null || _task_isOverDue1 === void 0 ? void 0 : _task_isOverDue1.days,\n                    expires: task.expires,\n                    extractedDays: days,\n                    isBetween\n                });\n                return isBetween;\n            });\n            const over90Tasks = extractedDays.filter((task)=>{\n                var _task_isOverDue, _task_isOverDue1;\n                // Try to extract days from the string first\n                const match = (((_task_isOverDue = task.isOverDue) === null || _task_isOverDue === void 0 ? void 0 : _task_isOverDue.days) || \"\").match(/(\\d+)/);\n                let days = match ? parseInt(match[0], 10) : null;\n                // If no days from string, calculate from expires date\n                if (days === null && task.expires) {\n                    const today = new Date();\n                    const expiresDate = new Date(task.expires);\n                    days = Math.ceil((expiresDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n                }\n                const isOver90 = days !== null && days > 90;\n                console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Over 90 check:\", {\n                    id: task.id,\n                    daysString: (_task_isOverDue1 = task.isOverDue) === null || _task_isOverDue1 === void 0 ? void 0 : _task_isOverDue1.days,\n                    expires: task.expires,\n                    extractedDays: days,\n                    isOver90\n                });\n                return isOver90;\n            });\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Time bucket results:\");\n            console.log(\"  - Under 30 days:\", under30Tasks.length);\n            console.log(\"  - 30-90 days:\", between30and90Tasks.length);\n            console.log(\"  - Over 90 days:\", over90Tasks.length);\n            setUnder30(under30Tasks.length);\n            setBetween30and90(between30and90Tasks.length);\n            setOver90(over90Tasks.length);\n        }\n    }, [\n        extractedDays\n    ]);\n    const chartData = [\n        {\n            title: \"Tasks overdue\",\n            amount: overdueTasks || 0,\n            fill: \"var(--color-overdue)\",\n            stroke: \"hsl(1, 97%, 60%)\"\n        },\n        {\n            title: \"Tasks due < 30\",\n            amount: under30 || 0,\n            fill: \"var(--color-thirtyDays)\",\n            stroke: \"hsl(205, 78%, 48%)\"\n        },\n        {\n            title: \"Tasks due 30 - 90\",\n            amount: between30and90 || 0,\n            fill: \"var(--color-thirtyToNinety)\",\n            stroke: \"hsl(205, 32%, 45%)\"\n        },\n        {\n            title: \"Tasks due > 90\",\n            amount: over90 || 0,\n            fill: \"var(--color-ninetyPlus)\",\n            stroke: \"hsl(174, 100%, 40%)\"\n        }\n    ];\n    // Log final chart data before rendering\n    console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Final chart data:\", chartData);\n    console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Chart data summary:\", {\n        overdue: overdueTasks || 0,\n        under30: under30 || 0,\n        between30and90: between30and90 || 0,\n        over90: over90 || 0,\n        total: (overdueTasks || 0) + (under30 || 0) + (between30and90 || 0) + (over90 || 0)\n    });\n    const chartConfig = {\n        amount: {\n            label: \"Amount\"\n        },\n        overdue: {\n            label: \"Tasks overdue\",\n            color: \"var(--chart-1)\"\n        },\n        thirtyDays: {\n            label: \"Tasks due under 30-days\",\n            color: \"var(--chart-3)\"\n        },\n        thirtyToNinety: {\n            label: \"Tasks due 30-90 days\",\n            color: \"var(--chart-4)\"\n        },\n        ninetyPlus: {\n            label: \"Tasks due 90+ days\",\n            color: \"var(--chart-5)\"\n        }\n    };\n    // Log render information\n    console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Component rendering with:\", {\n        isLoading,\n        hasMaintenanceChecks: !!maintenanceChecks,\n        maintenanceChecksCount: (maintenanceChecks === null || maintenanceChecks === void 0 ? void 0 : maintenanceChecks.length) || 0,\n        chartDataValid: chartData.every((item)=>typeof item.amount === \"number\"),\n        chartDataHasValues: chartData.some((item)=>item.amount > 0)\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col text-center\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.P, {\n                children: \"Tasks due - days\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\kpis\\\\maintance-pie-chart\\\\maintance-pie-chart.tsx\",\n                lineNumber: 539,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_pie_chart__WEBPACK_IMPORTED_MODULE_4__.PieChartComponent, {\n                chartData: chartData,\n                chartConfig: chartConfig\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\kpis\\\\maintance-pie-chart\\\\maintance-pie-chart.tsx\",\n                lineNumber: 540,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\kpis\\\\maintance-pie-chart\\\\maintance-pie-chart.tsx\",\n        lineNumber: 538,\n        columnNumber: 9\n    }, this);\n}\n_s(MaintenancePieChart, \"M4O8AtZ5QmlugdMuig8xghx4pTE=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_7__.useLazyQuery\n    ];\n});\n_c = MaintenancePieChart;\nvar _c;\n$RefreshReg$(_c, \"MaintenancePieChart\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/kpis/maintance-pie-chart/maintance-pie-chart.tsx\n"));

/***/ })

});