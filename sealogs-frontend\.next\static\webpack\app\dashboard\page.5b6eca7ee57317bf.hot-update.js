"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/ui/kpis/maintance-pie-chart/maintance-pie-chart.tsx":
/*!*********************************************************************!*\
  !*** ./src/app/ui/kpis/maintance-pie-chart/maintance-pie-chart.tsx ***!
  \*********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ MaintenancePieChart; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _components_pie_chart__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/pie-chart */ \"(app-pages-browser)/./src/components/pie-chart.tsx\");\n/* harmony import */ var _queries__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./queries */ \"(app-pages-browser)/./src/app/ui/kpis/maintance-pie-chart/queries.ts\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction MaintenancePieChart() {\n    _s();\n    const [maintenanceChecks, setMaintenanceChecks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [keywordFilter, setKeywordFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [edit_task, setEdit_task] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [overdueTasks, setOverdueTasks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [upcomingTasks, setUpcomingTasks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [extractedDays, setExtractedDays] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [under30, setUnder30] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [between30and90, setBetween30and90] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [over90, setOver90] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const init_permissions = ()=>{\n        if (permissions) {\n            if ((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_3__.hasPermission)(\"EDIT_TASK\", permissions)) {\n                setEdit_task(true);\n            } else {\n                setEdit_task(false);\n            }\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_3__.getPermissions);\n        init_permissions();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        init_permissions();\n    }, [\n        permissions\n    ]);\n    const [queryMaintenanceChecks] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_7__.useLazyQuery)(_queries__WEBPACK_IMPORTED_MODULE_5__.ReadComponentMaintenanceChecks, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readComponentMaintenanceChecks.nodes;\n            if (data) {\n                handleSetMaintenanceChecks(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryMaintenanceChecks error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            // loadVessels()\n            loadMaintenanceChecks();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const loadMaintenanceChecks = async function() {\n        let searchFilter = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {\n            ...filter\n        }, searchkeywordFilter = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : keywordFilter;\n        if (searchkeywordFilter.length > 0) {\n            const promises = searchkeywordFilter.map(async (keywordFilter)=>{\n                return await queryMaintenanceChecks({\n                    variables: {\n                        filter: {\n                            ...searchFilter,\n                            ...keywordFilter\n                        }\n                    }\n                });\n            });\n            let responses = await Promise.all(promises);\n            // filter out empty results\n            responses = responses.filter((r)=>r.data.readComponentMaintenanceChecks.nodes.length > 0);\n            // flatten results\n            responses = responses.flatMap((r)=>r.data.readComponentMaintenanceChecks.nodes);\n            // filter out duplicates\n            responses = responses.filter((value, index, self)=>self.findIndex((v)=>v.id === value.id) === index);\n            handleSetMaintenanceChecks(responses);\n        } else {\n            await queryMaintenanceChecks({\n                variables: {\n                    filter: searchFilter\n                }\n            });\n        }\n    };\n    const handleSetMaintenanceChecks = (tasks)=>{\n        const activeTasks = tasks.filter((task)=>task.archived === false).map((task)=>({\n                ...task,\n                isOverDue: (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_2__.isOverDueTask)(task)\n            }));\n        setMaintenanceChecks(activeTasks);\n        const appendedData = Array.from(new Set(activeTasks.filter((item)=>item.assignedToID > 0).map((item)=>item.assignedToID)));\n        loadCrewMemberInfo(appendedData);\n    };\n    // const [queryCrewMemberInfo] = useLazyQuery(GET_CREW_BY_IDS, {\n    //     fetchPolicy: 'cache-and-network',\n    //     onCompleted: (response: any) => {\n    //         const data = response.readSeaLogsMembers.nodes\n    //         if (data) {\n    //             // setCrewInfo(data)\n    //         }\n    //     },\n    //     onError: (error) => {\n    //         console.error('queryCrewMemberInfo error', error)\n    //     },\n    // })\n    // const loadCrewMemberInfo = async (crewId: any) => {\n    //     await queryCrewMemberInfo({\n    //         variables: {\n    //             crewMemberIDs: crewId.length > 0 ? crewId : [0],\n    //         },\n    //     })\n    // }\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (maintenanceChecks) {\n            setOverdueTasks(maintenanceChecks.filter((task)=>task.isOverDue.status === \"High\" && task.isOverDue.days.includes(\"days\")).length);\n            setUpcomingTasks(maintenanceChecks.filter((task)=>task.isOverDue.status === \"Upcoming\"));\n        }\n    }, [\n        maintenanceChecks\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (upcomingTasks) {\n            setExtractedDays(upcomingTasks.filter((task)=>task.isOverDue.days.includes(\"days\")));\n        }\n    }, [\n        upcomingTasks\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (extractedDays) {\n            setUnder30(extractedDays.filter((task)=>{\n                const match = (task.isOverDue.days || \"\").match(/(\\d+)/);\n                const days = match ? parseInt(match[0], 10) : null;\n                return days !== null && days < 30;\n            }).length);\n            setBetween30and90(extractedDays.filter((task)=>{\n                const match = (task.isOverDue.days || \"\").match(/(\\d+)/);\n                const days = match ? parseInt(match[0], 10) : null;\n                return days !== null && days >= 30 && days <= 90;\n            }).length);\n            setOver90(extractedDays.filter((task)=>{\n                const match = (task.isOverDue.days || \"\").match(/(\\d+)/);\n                const days = match ? parseInt(match[0], 10) : null;\n                return days !== null && days > 90;\n            }).length);\n        }\n    }, [\n        extractedDays\n    ]);\n    const chartData = [\n        {\n            title: \"Tasks overdue\",\n            amount: overdueTasks,\n            fill: \"var(--color-overdue)\",\n            stroke: \"hsl(1, 97%, 60%)\"\n        },\n        {\n            title: \"Tasks due < 30\",\n            amount: under30,\n            fill: \"var(--color-thirtyDays)\",\n            stroke: \"hsl(205, 78%, 48%)\"\n        },\n        {\n            title: \"Tasks due 30 - 90\",\n            amount: between30and90,\n            fill: \"var(--color-thirtyToNinety)\",\n            stroke: \"hsl(205, 32%, 45%)\"\n        },\n        {\n            title: \"Tasks due > 90\",\n            amount: over90,\n            fill: \"var(--color-ninetyPlus)\",\n            stroke: \"hsl(174, 100%, 40%)\"\n        }\n    ];\n    const chartConfig = {\n        amount: {\n            label: \"Amount\"\n        },\n        overdue: {\n            label: \"Tasks overdue\",\n            color: \"var(--chart-1)\"\n        },\n        thirtyDays: {\n            label: \"Tasks due under 30-days\",\n            color: \"var(--chart-3)\"\n        },\n        thirtyToNinety: {\n            label: \"Tasks due 30-90 days\",\n            color: \"var(--chart-4)\"\n        },\n        ninetyPlus: {\n            label: \"Tasks due 90+ days\",\n            color: \"var(--chart-5)\"\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col text-center\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.P, {\n                children: \"Tasks due - days\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\kpis\\\\maintance-pie-chart\\\\maintance-pie-chart.tsx\",\n                lineNumber: 251,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_pie_chart__WEBPACK_IMPORTED_MODULE_4__.PieChartComponent, {\n                chartData: chartData,\n                chartConfig: chartConfig\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\kpis\\\\maintance-pie-chart\\\\maintance-pie-chart.tsx\",\n                lineNumber: 252,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\kpis\\\\maintance-pie-chart\\\\maintance-pie-chart.tsx\",\n        lineNumber: 250,\n        columnNumber: 9\n    }, this);\n}\n_s(MaintenancePieChart, \"M4O8AtZ5QmlugdMuig8xghx4pTE=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_7__.useLazyQuery\n    ];\n});\n_c = MaintenancePieChart;\nvar _c;\n$RefreshReg$(_c, \"MaintenancePieChart\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/kpis/maintance-pie-chart/maintance-pie-chart.tsx\n"));

/***/ })

});