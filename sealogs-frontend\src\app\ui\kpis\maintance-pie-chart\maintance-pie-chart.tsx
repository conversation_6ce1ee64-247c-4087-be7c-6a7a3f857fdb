'use client'
import React, { useEffect, useState } from 'react'
import { isOverDueTask } from '@/app/lib/actions'
import { useLazyQuery } from '@apollo/client'
import { getPermissions, hasPermission } from '@/app/helpers/userHelper'
import { PieChartComponent } from '@/components/pie-chart'
import { ChartConfig } from '@/components/ui/chart'
import { ReadComponentMaintenanceChecks } from './queries'
import { P } from '@/components/ui'

export default function MaintenancePieChart() {
    const [maintenanceChecks, setMaintenanceChecks] = useState<any>()

    const [filter, setFilter] = useState({} as SearchFilter)
    const [isLoading, setIsLoading] = useState(true)
    const [keywordFilter, setKeywordFilter] = useState([] as any)
    const [permissions, setPermissions] = useState<any>(false)
    const [edit_task, setEdit_task] = useState<any>(false)
    const [overdueTasks, setOverdueTasks] = useState<any>()
    const [upcomingTasks, setUpcomingTasks] = useState<any>()
    const [extractedDays, setExtractedDays] = useState<any>()
    const [under30, setUnder30] = useState<any>()
    const [between30and90, setBetween30and90] = useState<any>()
    const [over90, setOver90] = useState<any>()

    const init_permissions = () => {
        if (permissions) {
            if (hasPermission('EDIT_TASK', permissions)) {
                setEdit_task(true)
            } else {
                setEdit_task(false)
            }
        }
    }

    useEffect(() => {
        setPermissions(getPermissions)
        init_permissions()
    }, [])

    useEffect(() => {
        init_permissions()
    }, [permissions])

    const [queryMaintenanceChecks] = useLazyQuery(
        ReadComponentMaintenanceChecks,
        {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                console.log(
                    '🔍 [MaintenancePieChart] Raw GraphQL Response:',
                    response,
                )
                const data = response.readComponentMaintenanceChecks.nodes
                console.log(
                    '🔍 [MaintenancePieChart] Extracted nodes data:',
                    data,
                )
                console.log(
                    '🔍 [MaintenancePieChart] Total maintenance checks received:',
                    data?.length || 0,
                )
                if (data) {
                    handleSetMaintenanceChecks(data)
                } else {
                    console.warn(
                        '⚠️ [MaintenancePieChart] No data received from GraphQL query',
                    )
                }
            },
            onError: (error: any) => {
                console.error(
                    '❌ [MaintenancePieChart] queryMaintenanceChecks error',
                    error,
                )
            },
        },
    )
    useEffect(() => {
        if (isLoading) {
            // loadVessels()
            loadMaintenanceChecks()
            setIsLoading(false)
        }
    }, [isLoading])
    const loadMaintenanceChecks = async (
        searchFilter: SearchFilter = { ...filter },
        searchkeywordFilter: any = keywordFilter,
    ) => {
        if (searchkeywordFilter.length > 0) {
            const promises = searchkeywordFilter.map(
                async (keywordFilter: any) => {
                    return await queryMaintenanceChecks({
                        variables: {
                            filter: { ...searchFilter, ...keywordFilter },
                        },
                    })
                },
            )
            let responses = await Promise.all(promises)
            // filter out empty results
            responses = responses.filter(
                (r: any) =>
                    r.data.readComponentMaintenanceChecks.nodes.length > 0,
            )
            // flatten results
            responses = responses.flatMap(
                (r: any) => r.data.readComponentMaintenanceChecks.nodes,
            )
            // filter out duplicates
            responses = responses.filter(
                (value: any, index: any, self: any) =>
                    self.findIndex((v: any) => v.id === value.id) === index,
            )
            handleSetMaintenanceChecks(responses)
        } else {
            await queryMaintenanceChecks({
                variables: {
                    filter: searchFilter,
                },
            })
        }
    }

    const handleSetMaintenanceChecks = (tasks: any) => {
        console.log(
            '🔍 [MaintenancePieChart] Processing tasks in handleSetMaintenanceChecks:',
            tasks,
        )
        console.log(
            '🔍 [MaintenancePieChart] Total tasks before filtering:',
            tasks?.length || 0,
        )

        const activeTasks = tasks
            .filter((task: any) => {
                const isActive = task.archived === false
                if (!isActive) {
                    console.log(
                        '🔍 [MaintenancePieChart] Filtered out archived task:',
                        task.id,
                    )
                }
                return isActive
            })
            .map((task: any) => {
                const overDueInfo = isOverDueTask(task)
                console.log('🔍 [MaintenancePieChart] Task processing:', {
                    id: task.id,
                    status: task.status,
                    expires: task.expires,
                    startDate: task.startDate,
                    isOverDueInfo: overDueInfo,
                })
                return {
                    ...task,
                    isOverDue: overDueInfo,
                }
            })

        console.log(
            '🔍 [MaintenancePieChart] Active tasks after processing:',
            activeTasks,
        )
        console.log(
            '🔍 [MaintenancePieChart] Total active tasks:',
            activeTasks.length,
        )

        // Log status distribution
        const statusDistribution = activeTasks.reduce((acc: any, task: any) => {
            const status = task.isOverDue?.status || 'Unknown'
            acc[status] = (acc[status] || 0) + 1
            return acc
        }, {})
        console.log(
            '🔍 [MaintenancePieChart] Status distribution:',
            statusDistribution,
        )

        setMaintenanceChecks(activeTasks)
        // const appendedData: number[] = Array.from(
        //     new Set(
        //         activeTasks
        //             .filter((item: any) => item.assignedToID > 0)
        //             .map((item: any) => item.assignedToID),
        //     ),
        // )
        // loadCrewMemberInfo(appendedData)
    }

    // const [queryCrewMemberInfo] = useLazyQuery(GET_CREW_BY_IDS, {
    //     fetchPolicy: 'cache-and-network',
    //     onCompleted: (response: any) => {
    //         const data = response.readSeaLogsMembers.nodes
    //         if (data) {
    //             // setCrewInfo(data)
    //         }
    //     },
    //     onError: (error) => {
    //         console.error('queryCrewMemberInfo error', error)
    //     },
    // })
    // const loadCrewMemberInfo = async (crewId: any) => {
    //     await queryCrewMemberInfo({
    //         variables: {
    //             crewMemberIDs: crewId.length > 0 ? crewId : [0],
    //         },
    //     })
    // }

    useEffect(() => {
        if (maintenanceChecks) {
            console.log(
                '🔍 [MaintenancePieChart] Processing maintenanceChecks for overdue/upcoming tasks',
            )
            console.log(
                '🔍 [MaintenancePieChart] Total maintenance checks to process:',
                maintenanceChecks.length,
            )

            // Log all task statuses and days for debugging
            maintenanceChecks.forEach((task: any, index: number) => {
                console.log(`🔍 [MaintenancePieChart] Task ${index + 1}:`, {
                    id: task.id,
                    status: task.status,
                    isOverDueStatus: task.isOverDue?.status,
                    isOverDueDays: task.isOverDue?.days,
                    expires: task.expires,
                })
            })

            const overdueTasks = maintenanceChecks.filter((task: any) => {
                const isHighStatus = task.isOverDue?.status === 'High'
                const hasDaysString = task.isOverDue?.days?.includes('days')
                const isOverdue = isHighStatus && hasDaysString

                if (isHighStatus) {
                    console.log('🔍 [MaintenancePieChart] High status task:', {
                        id: task.id,
                        days: task.isOverDue.days,
                        hasDaysString,
                        isOverdue,
                    })
                }

                return isOverdue
            })

            const upcomingTasks = maintenanceChecks.filter((task: any) => {
                const isUpcoming = task.isOverDue?.status === 'Upcoming'
                if (isUpcoming) {
                    console.log('🔍 [MaintenancePieChart] Upcoming task:', {
                        id: task.id,
                        days: task.isOverDue.days,
                    })
                }
                return isUpcoming
            })

            console.log(
                '🔍 [MaintenancePieChart] Overdue tasks count:',
                overdueTasks.length,
            )
            console.log(
                '🔍 [MaintenancePieChart] Upcoming tasks count:',
                upcomingTasks.length,
            )
            console.log('🔍 [MaintenancePieChart] Overdue tasks:', overdueTasks)
            console.log(
                '🔍 [MaintenancePieChart] Upcoming tasks:',
                upcomingTasks,
            )

            setOverdueTasks(overdueTasks.length)
            setUpcomingTasks(upcomingTasks)
        }
    }, [maintenanceChecks])

    useEffect(() => {
        if (upcomingTasks) {
            console.log(
                '🔍 [MaintenancePieChart] Processing upcomingTasks for day extraction',
            )
            console.log(
                '🔍 [MaintenancePieChart] Upcoming tasks to process:',
                upcomingTasks.length,
            )

            const extractedDays = upcomingTasks.filter((task: any) => {
                const hasDaysString = task.isOverDue?.days?.includes('days')
                console.log(
                    '🔍 [MaintenancePieChart] Checking task for days string:',
                    {
                        id: task.id,
                        days: task.isOverDue?.days,
                        hasDaysString,
                    },
                )
                return hasDaysString
            })

            console.log(
                '🔍 [MaintenancePieChart] Extracted days tasks count:',
                extractedDays.length,
            )
            console.log(
                '🔍 [MaintenancePieChart] Extracted days tasks:',
                extractedDays,
            )

            setExtractedDays(extractedDays)
        }
    }, [upcomingTasks])

    useEffect(() => {
        if (extractedDays) {
            console.log(
                '🔍 [MaintenancePieChart] Processing extractedDays for time buckets',
            )
            console.log(
                '🔍 [MaintenancePieChart] Extracted days to categorize:',
                extractedDays.length,
            )

            const under30Tasks = extractedDays.filter((task: any) => {
                const match = (task.isOverDue?.days || '').match(/(\d+)/)
                const days = match ? parseInt(match[0], 10) : null
                const isUnder30 = days !== null && days < 30

                console.log('🔍 [MaintenancePieChart] Under 30 check:', {
                    id: task.id,
                    daysString: task.isOverDue?.days,
                    extractedDays: days,
                    isUnder30,
                })

                return isUnder30
            })

            const between30and90Tasks = extractedDays.filter((task: any) => {
                const match = (task.isOverDue?.days || '').match(/(\d+)/)
                const days = match ? parseInt(match[0], 10) : null
                const isBetween = days !== null && days >= 30 && days <= 90

                console.log('🔍 [MaintenancePieChart] 30-90 check:', {
                    id: task.id,
                    daysString: task.isOverDue?.days,
                    extractedDays: days,
                    isBetween,
                })

                return isBetween
            })

            const over90Tasks = extractedDays.filter((task: any) => {
                const match = (task.isOverDue?.days || '').match(/(\d+)/)
                const days = match ? parseInt(match[0], 10) : null
                const isOver90 = days !== null && days > 90

                console.log('🔍 [MaintenancePieChart] Over 90 check:', {
                    id: task.id,
                    daysString: task.isOverDue?.days,
                    extractedDays: days,
                    isOver90,
                })

                return isOver90
            })

            console.log('🔍 [MaintenancePieChart] Time bucket results:')
            console.log('  - Under 30 days:', under30Tasks.length)
            console.log('  - 30-90 days:', between30and90Tasks.length)
            console.log('  - Over 90 days:', over90Tasks.length)

            setUnder30(under30Tasks.length)
            setBetween30and90(between30and90Tasks.length)
            setOver90(over90Tasks.length)
        }
    }, [extractedDays])

    const chartData = [
        {
            title: 'Tasks overdue',
            amount: overdueTasks || 0,
            fill: 'var(--color-overdue)',
            stroke: 'hsl(1, 97%, 60%)',
        },
        {
            title: 'Tasks due < 30',
            amount: under30 || 0,
            fill: 'var(--color-thirtyDays)',
            stroke: 'hsl(205, 78%, 48%)',
        },
        {
            title: 'Tasks due 30 - 90',
            amount: between30and90 || 0,
            fill: 'var(--color-thirtyToNinety)',
            stroke: 'hsl(205, 32%, 45%)',
        },
        {
            title: 'Tasks due > 90',
            amount: over90 || 0,
            fill: 'var(--color-ninetyPlus)',
            stroke: 'hsl(174, 100%, 40%)',
        },
        //{ title: "Completed", amount: 2, fill: "var(--color-completed)" },
    ]

    // Log final chart data before rendering
    console.log('🔍 [MaintenancePieChart] Final chart data:', chartData)
    console.log('🔍 [MaintenancePieChart] Chart data summary:', {
        overdue: overdueTasks || 0,
        under30: under30 || 0,
        between30and90: between30and90 || 0,
        over90: over90 || 0,
        total:
            (overdueTasks || 0) +
            (under30 || 0) +
            (between30and90 || 0) +
            (over90 || 0),
    })
    const chartConfig = {
        amount: {
            label: 'Amount',
        },
        overdue: {
            label: 'Tasks overdue',
            color: 'var(--chart-1)',
        },
        thirtyDays: {
            label: 'Tasks due under 30-days',
            color: 'var(--chart-3)',
        },
        thirtyToNinety: {
            label: 'Tasks due 30-90 days',
            color: 'var(--chart-4)',
        },
        ninetyPlus: {
            label: 'Tasks due 90+ days',
            color: 'var(--chart-5)',
        },
    } satisfies ChartConfig

    return (
        <div className="flex flex-col text-center">
            <P>Tasks due - days</P>
            <PieChartComponent
                chartData={chartData}
                chartConfig={chartConfig}
            />
        </div>
    )
}
