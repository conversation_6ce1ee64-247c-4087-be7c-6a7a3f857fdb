"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/ui/kpis/maintance-pie-chart/maintance-pie-chart.tsx":
/*!*********************************************************************!*\
  !*** ./src/app/ui/kpis/maintance-pie-chart/maintance-pie-chart.tsx ***!
  \*********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ MaintenancePieChart; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _components_pie_chart__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/pie-chart */ \"(app-pages-browser)/./src/components/pie-chart.tsx\");\n/* harmony import */ var _queries__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./queries */ \"(app-pages-browser)/./src/app/ui/kpis/maintance-pie-chart/queries.ts\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction MaintenancePieChart() {\n    _s();\n    const [maintenanceChecks, setMaintenanceChecks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [keywordFilter, setKeywordFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [edit_task, setEdit_task] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [overdueTasks, setOverdueTasks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [upcomingTasks, setUpcomingTasks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [extractedDays, setExtractedDays] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [under30, setUnder30] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [between30and90, setBetween30and90] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [over90, setOver90] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [openTasks, setOpenTasks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const init_permissions = ()=>{\n        if (permissions) {\n            if ((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_3__.hasPermission)(\"EDIT_TASK\", permissions)) {\n                setEdit_task(true);\n            } else {\n                setEdit_task(false);\n            }\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_3__.getPermissions);\n        init_permissions();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        init_permissions();\n    }, [\n        permissions\n    ]);\n    const [queryMaintenanceChecks] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_7__.useLazyQuery)(_queries__WEBPACK_IMPORTED_MODULE_5__.ReadComponentMaintenanceChecks, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Raw GraphQL Response:\", response);\n            const data = response.readComponentMaintenanceChecks.nodes;\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Extracted nodes data:\", data);\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Total maintenance checks received:\", (data === null || data === void 0 ? void 0 : data.length) || 0);\n            if (data) {\n                handleSetMaintenanceChecks(data);\n            } else {\n                console.warn(\"⚠️ [MaintenancePieChart] No data received from GraphQL query\");\n            }\n        },\n        onError: (error)=>{\n            console.error(\"❌ [MaintenancePieChart] queryMaintenanceChecks error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            // loadVessels()\n            loadMaintenanceChecks();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const loadMaintenanceChecks = async function() {\n        let searchFilter = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {\n            ...filter\n        }, searchkeywordFilter = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : keywordFilter;\n        if (searchkeywordFilter.length > 0) {\n            const promises = searchkeywordFilter.map(async (keywordFilter)=>{\n                return await queryMaintenanceChecks({\n                    variables: {\n                        filter: {\n                            ...searchFilter,\n                            ...keywordFilter\n                        }\n                    }\n                });\n            });\n            let responses = await Promise.all(promises);\n            // filter out empty results\n            responses = responses.filter((r)=>r.data.readComponentMaintenanceChecks.nodes.length > 0);\n            // flatten results\n            responses = responses.flatMap((r)=>r.data.readComponentMaintenanceChecks.nodes);\n            // filter out duplicates\n            responses = responses.filter((value, index, self)=>self.findIndex((v)=>v.id === value.id) === index);\n            handleSetMaintenanceChecks(responses);\n        } else {\n            await queryMaintenanceChecks({\n                variables: {\n                    filter: searchFilter\n                }\n            });\n        }\n    };\n    const handleSetMaintenanceChecks = (tasks)=>{\n        console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Processing tasks in handleSetMaintenanceChecks:\", tasks);\n        console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Total tasks before filtering:\", (tasks === null || tasks === void 0 ? void 0 : tasks.length) || 0);\n        const activeTasks = tasks.filter((task)=>{\n            const isActive = task.archived === false;\n            if (!isActive) {\n                console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Filtered out archived task:\", task.id);\n            }\n            return isActive;\n        }).map((task)=>{\n            const overDueInfo = (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_2__.isOverDueTask)(task);\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Task processing:\", {\n                id: task.id,\n                status: task.status,\n                expires: task.expires,\n                startDate: task.startDate,\n                isOverDueInfo: overDueInfo\n            });\n            return {\n                ...task,\n                isOverDue: overDueInfo\n            };\n        });\n        console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Active tasks after processing:\", activeTasks);\n        console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Total active tasks:\", activeTasks.length);\n        // Log status distribution\n        const statusDistribution = activeTasks.reduce((acc, task)=>{\n            var _task_isOverDue;\n            const status = ((_task_isOverDue = task.isOverDue) === null || _task_isOverDue === void 0 ? void 0 : _task_isOverDue.status) || \"Unknown\";\n            acc[status] = (acc[status] || 0) + 1;\n            return acc;\n        }, {});\n        console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Status distribution:\", statusDistribution);\n        setMaintenanceChecks(activeTasks);\n    // const appendedData: number[] = Array.from(\n    //     new Set(\n    //         activeTasks\n    //             .filter((item: any) => item.assignedToID > 0)\n    //             .map((item: any) => item.assignedToID),\n    //     ),\n    // )\n    // loadCrewMemberInfo(appendedData)\n    };\n    // const [queryCrewMemberInfo] = useLazyQuery(GET_CREW_BY_IDS, {\n    //     fetchPolicy: 'cache-and-network',\n    //     onCompleted: (response: any) => {\n    //         const data = response.readSeaLogsMembers.nodes\n    //         if (data) {\n    //             // setCrewInfo(data)\n    //         }\n    //     },\n    //     onError: (error) => {\n    //         console.error('queryCrewMemberInfo error', error)\n    //     },\n    // })\n    // const loadCrewMemberInfo = async (crewId: any) => {\n    //     await queryCrewMemberInfo({\n    //         variables: {\n    //             crewMemberIDs: crewId.length > 0 ? crewId : [0],\n    //         },\n    //     })\n    // }\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (maintenanceChecks) {\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Processing maintenanceChecks for overdue/upcoming tasks\");\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Total maintenance checks to process:\", maintenanceChecks.length);\n            // Log all task statuses and days for debugging\n            maintenanceChecks.forEach((task, index)=>{\n                var _task_isOverDue, _task_isOverDue1;\n                console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Task \".concat(index + 1, \":\"), {\n                    id: task.id,\n                    status: task.status,\n                    isOverDueStatus: (_task_isOverDue = task.isOverDue) === null || _task_isOverDue === void 0 ? void 0 : _task_isOverDue.status,\n                    isOverDueDays: (_task_isOverDue1 = task.isOverDue) === null || _task_isOverDue1 === void 0 ? void 0 : _task_isOverDue1.days,\n                    expires: task.expires\n                });\n            });\n            const overdueTasks = maintenanceChecks.filter((task)=>{\n                var _task_isOverDue, _task_isOverDue_days, _task_isOverDue1;\n                const isHighStatus = ((_task_isOverDue = task.isOverDue) === null || _task_isOverDue === void 0 ? void 0 : _task_isOverDue.status) === \"High\";\n                const hasDaysString = (_task_isOverDue1 = task.isOverDue) === null || _task_isOverDue1 === void 0 ? void 0 : (_task_isOverDue_days = _task_isOverDue1.days) === null || _task_isOverDue_days === void 0 ? void 0 : _task_isOverDue_days.includes(\"days\");\n                const isOverdue = isHighStatus && hasDaysString;\n                if (isHighStatus) {\n                    console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] High status task:\", {\n                        id: task.id,\n                        days: task.isOverDue.days,\n                        hasDaysString,\n                        isOverdue\n                    });\n                }\n                return isOverdue;\n            });\n            const upcomingTasks = maintenanceChecks.filter((task)=>{\n                var _task_isOverDue, _task_isOverDue1, _task_isOverDue2;\n                // Include tasks with 'Upcoming', 'Medium', 'Low' status AND tasks that have actual due dates\n                const hasUpcomingStatus = [\n                    \"Upcoming\",\n                    \"Medium\",\n                    \"Low\"\n                ].includes((_task_isOverDue = task.isOverDue) === null || _task_isOverDue === void 0 ? void 0 : _task_isOverDue.status);\n                // Also include tasks that have expires dates and aren't completed or high priority overdue\n                const hasValidDueDate = task.expires && ![\n                    \"Completed\",\n                    \"High\"\n                ].includes((_task_isOverDue1 = task.isOverDue) === null || _task_isOverDue1 === void 0 ? void 0 : _task_isOverDue1.status) && ((_task_isOverDue2 = task.isOverDue) === null || _task_isOverDue2 === void 0 ? void 0 : _task_isOverDue2.days) !== \"Open\";\n                const isUpcoming = hasUpcomingStatus || hasValidDueDate;\n                if (isUpcoming) {\n                    var _task_isOverDue3;\n                    console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Upcoming task:\", {\n                        id: task.id,\n                        status: (_task_isOverDue3 = task.isOverDue) === null || _task_isOverDue3 === void 0 ? void 0 : _task_isOverDue3.status,\n                        days: task.isOverDue.days,\n                        expires: task.expires,\n                        hasUpcomingStatus,\n                        hasValidDueDate\n                    });\n                }\n                return isUpcoming;\n            });\n            // Count open tasks (tasks with no specific due dates)\n            const openTasksCount = maintenanceChecks.filter((task)=>{\n                var _task_isOverDue, _task_isOverDue1;\n                const isOpen = ((_task_isOverDue = task.isOverDue) === null || _task_isOverDue === void 0 ? void 0 : _task_isOverDue.days) === \"Open\" && ![\n                    \"High\",\n                    \"Completed\"\n                ].includes((_task_isOverDue1 = task.isOverDue) === null || _task_isOverDue1 === void 0 ? void 0 : _task_isOverDue1.status);\n                if (isOpen) {\n                    var _task_isOverDue2;\n                    console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Open task:\", {\n                        id: task.id,\n                        status: (_task_isOverDue2 = task.isOverDue) === null || _task_isOverDue2 === void 0 ? void 0 : _task_isOverDue2.status,\n                        days: task.isOverDue.days\n                    });\n                }\n                return isOpen;\n            });\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Overdue tasks count:\", overdueTasks.length);\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Upcoming tasks count:\", upcomingTasks.length);\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Open tasks count:\", openTasksCount.length);\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Overdue tasks:\", overdueTasks);\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Upcoming tasks:\", upcomingTasks);\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Open tasks:\", openTasksCount);\n            setOverdueTasks(overdueTasks.length);\n            setUpcomingTasks(upcomingTasks);\n            setOpenTasks(openTasksCount.length);\n        }\n    }, [\n        maintenanceChecks\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (upcomingTasks) {\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Processing upcomingTasks for day extraction\");\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Upcoming tasks to process:\", upcomingTasks.length);\n            const extractedDays = upcomingTasks.filter((task)=>{\n                var _task_isOverDue;\n                const daysString = ((_task_isOverDue = task.isOverDue) === null || _task_isOverDue === void 0 ? void 0 : _task_isOverDue.days) || \"\";\n                // Check if the days string contains a number (for both \"Due - X days\" and \"X days ago\" formats)\n                const hasNumericDays = /\\d+/.test(daysString) && daysString.includes(\"days\");\n                // Exclude tasks with \"Open\" status or empty days\n                const isValidDaysFormat = hasNumericDays && !daysString.includes(\"Open\");\n                // For tasks without valid day strings but with expires dates, calculate days manually\n                let hasCalculatedDays = false;\n                if (!isValidDaysFormat && task.expires) {\n                    const today = new Date();\n                    const expiresDate = new Date(task.expires);\n                    const daysDiff = Math.ceil((expiresDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n                    hasCalculatedDays = daysDiff > 0 // Only include future dates\n                    ;\n                }\n                const isValid = isValidDaysFormat || hasCalculatedDays;\n                console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Checking task for days string:\", {\n                    id: task.id,\n                    days: daysString,\n                    expires: task.expires,\n                    hasNumericDays,\n                    isValidDaysFormat,\n                    hasCalculatedDays,\n                    isValid\n                });\n                return isValid;\n            });\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Extracted days tasks count:\", extractedDays.length);\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Extracted days tasks:\", extractedDays);\n            setExtractedDays(extractedDays);\n        }\n    }, [\n        upcomingTasks\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (extractedDays) {\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Processing extractedDays for time buckets\");\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Extracted days to categorize:\", extractedDays.length);\n            const under30Tasks = extractedDays.filter((task)=>{\n                var _task_isOverDue, _task_isOverDue1;\n                // Try to extract days from the string first\n                const match = (((_task_isOverDue = task.isOverDue) === null || _task_isOverDue === void 0 ? void 0 : _task_isOverDue.days) || \"\").match(/(\\d+)/);\n                let days = match ? parseInt(match[0], 10) : null;\n                // If no days from string, calculate from expires date\n                if (days === null && task.expires) {\n                    const today = new Date();\n                    const expiresDate = new Date(task.expires);\n                    days = Math.ceil((expiresDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n                }\n                const isUnder30 = days !== null && days > 0 && days < 30;\n                console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Under 30 check:\", {\n                    id: task.id,\n                    daysString: (_task_isOverDue1 = task.isOverDue) === null || _task_isOverDue1 === void 0 ? void 0 : _task_isOverDue1.days,\n                    expires: task.expires,\n                    extractedDays: days,\n                    isUnder30\n                });\n                return isUnder30;\n            });\n            const between30and90Tasks = extractedDays.filter((task)=>{\n                var _task_isOverDue, _task_isOverDue1;\n                // Try to extract days from the string first\n                const match = (((_task_isOverDue = task.isOverDue) === null || _task_isOverDue === void 0 ? void 0 : _task_isOverDue.days) || \"\").match(/(\\d+)/);\n                let days = match ? parseInt(match[0], 10) : null;\n                // If no days from string, calculate from expires date\n                if (days === null && task.expires) {\n                    const today = new Date();\n                    const expiresDate = new Date(task.expires);\n                    days = Math.ceil((expiresDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n                }\n                const isBetween = days !== null && days >= 30 && days <= 90;\n                console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] 30-90 check:\", {\n                    id: task.id,\n                    daysString: (_task_isOverDue1 = task.isOverDue) === null || _task_isOverDue1 === void 0 ? void 0 : _task_isOverDue1.days,\n                    expires: task.expires,\n                    extractedDays: days,\n                    isBetween\n                });\n                return isBetween;\n            });\n            const over90Tasks = extractedDays.filter((task)=>{\n                var _task_isOverDue, _task_isOverDue1;\n                // Try to extract days from the string first\n                const match = (((_task_isOverDue = task.isOverDue) === null || _task_isOverDue === void 0 ? void 0 : _task_isOverDue.days) || \"\").match(/(\\d+)/);\n                let days = match ? parseInt(match[0], 10) : null;\n                // If no days from string, calculate from expires date\n                if (days === null && task.expires) {\n                    const today = new Date();\n                    const expiresDate = new Date(task.expires);\n                    days = Math.ceil((expiresDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n                }\n                const isOver90 = days !== null && days > 90;\n                console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Over 90 check:\", {\n                    id: task.id,\n                    daysString: (_task_isOverDue1 = task.isOverDue) === null || _task_isOverDue1 === void 0 ? void 0 : _task_isOverDue1.days,\n                    expires: task.expires,\n                    extractedDays: days,\n                    isOver90\n                });\n                return isOver90;\n            });\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Time bucket results:\");\n            console.log(\"  - Under 30 days:\", under30Tasks.length);\n            console.log(\"  - 30-90 days:\", between30and90Tasks.length);\n            console.log(\"  - Over 90 days:\", over90Tasks.length);\n            setUnder30(under30Tasks.length);\n            setBetween30and90(between30and90Tasks.length);\n            setOver90(over90Tasks.length);\n        }\n    }, [\n        extractedDays\n    ]);\n    const chartData = [\n        {\n            title: \"Tasks overdue\",\n            amount: overdueTasks || 0,\n            fill: \"var(--color-overdue)\",\n            stroke: \"hsl(1, 97%, 60%)\"\n        },\n        {\n            title: \"Tasks due < 30\",\n            amount: under30 || 0,\n            fill: \"var(--color-thirtyDays)\",\n            stroke: \"hsl(205, 78%, 48%)\"\n        },\n        {\n            title: \"Tasks due 30 - 90\",\n            amount: between30and90 || 0,\n            fill: \"var(--color-thirtyToNinety)\",\n            stroke: \"hsl(205, 32%, 45%)\"\n        },\n        {\n            title: \"Tasks due > 90\",\n            amount: over90 || 0,\n            fill: \"var(--color-ninetyPlus)\",\n            stroke: \"hsl(174, 100%, 40%)\"\n        },\n        {\n            title: \"Open tasks\",\n            amount: openTasks || 0,\n            fill: \"var(--color-open)\",\n            stroke: \"hsl(220, 14%, 60%)\"\n        }\n    ];\n    // Log final chart data before rendering\n    console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Final chart data:\", chartData);\n    console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Chart data summary:\", {\n        overdue: overdueTasks || 0,\n        under30: under30 || 0,\n        between30and90: between30and90 || 0,\n        over90: over90 || 0,\n        total: (overdueTasks || 0) + (under30 || 0) + (between30and90 || 0) + (over90 || 0)\n    });\n    const chartConfig = {\n        amount: {\n            label: \"Amount\"\n        },\n        overdue: {\n            label: \"Tasks overdue\",\n            color: \"var(--chart-1)\"\n        },\n        thirtyDays: {\n            label: \"Tasks due under 30-days\",\n            color: \"var(--chart-3)\"\n        },\n        thirtyToNinety: {\n            label: \"Tasks due 30-90 days\",\n            color: \"var(--chart-4)\"\n        },\n        ninetyPlus: {\n            label: \"Tasks due 90+ days\",\n            color: \"var(--chart-5)\"\n        },\n        open: {\n            label: \"Open tasks (no due date)\",\n            color: \"var(--chart-6)\"\n        }\n    };\n    // Log render information\n    console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Component rendering with:\", {\n        isLoading,\n        hasMaintenanceChecks: !!maintenanceChecks,\n        maintenanceChecksCount: (maintenanceChecks === null || maintenanceChecks === void 0 ? void 0 : maintenanceChecks.length) || 0,\n        chartDataValid: chartData.every((item)=>typeof item.amount === \"number\"),\n        chartDataHasValues: chartData.some((item)=>item.amount > 0)\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col text-center\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.P, {\n                children: \"Tasks due - days\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\kpis\\\\maintance-pie-chart\\\\maintance-pie-chart.tsx\",\n                lineNumber: 571,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_pie_chart__WEBPACK_IMPORTED_MODULE_4__.PieChartComponent, {\n                chartData: chartData,\n                chartConfig: chartConfig\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\kpis\\\\maintance-pie-chart\\\\maintance-pie-chart.tsx\",\n                lineNumber: 572,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\kpis\\\\maintance-pie-chart\\\\maintance-pie-chart.tsx\",\n        lineNumber: 570,\n        columnNumber: 9\n    }, this);\n}\n_s(MaintenancePieChart, \"R5rGRBaiacgi5V4zjdZJmd+4i2Q=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_7__.useLazyQuery\n    ];\n});\n_c = MaintenancePieChart;\nvar _c;\n$RefreshReg$(_c, \"MaintenancePieChart\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/kpis/maintance-pie-chart/maintance-pie-chart.tsx\n"));

/***/ })

});