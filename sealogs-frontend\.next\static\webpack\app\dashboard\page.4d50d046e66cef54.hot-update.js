"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/ui/kpis/maintance-pie-chart/maintance-pie-chart.tsx":
/*!*********************************************************************!*\
  !*** ./src/app/ui/kpis/maintance-pie-chart/maintance-pie-chart.tsx ***!
  \*********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ MaintenancePieChart; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _components_pie_chart__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/pie-chart */ \"(app-pages-browser)/./src/components/pie-chart.tsx\");\n/* harmony import */ var _queries__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./queries */ \"(app-pages-browser)/./src/app/ui/kpis/maintance-pie-chart/queries.ts\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction MaintenancePieChart() {\n    _s();\n    const [maintenanceChecks, setMaintenanceChecks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [keywordFilter, setKeywordFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [edit_task, setEdit_task] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [overdueTasks, setOverdueTasks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [upcomingTasks, setUpcomingTasks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [extractedDays, setExtractedDays] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [under30, setUnder30] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [between30and90, setBetween30and90] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [over90, setOver90] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const init_permissions = ()=>{\n        if (permissions) {\n            if ((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_3__.hasPermission)(\"EDIT_TASK\", permissions)) {\n                setEdit_task(true);\n            } else {\n                setEdit_task(false);\n            }\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_3__.getPermissions);\n        init_permissions();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        init_permissions();\n    }, [\n        permissions\n    ]);\n    const [queryMaintenanceChecks] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_7__.useLazyQuery)(_queries__WEBPACK_IMPORTED_MODULE_5__.ReadComponentMaintenanceChecks, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Raw GraphQL Response:\", response);\n            const data = response.readComponentMaintenanceChecks.nodes;\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Extracted nodes data:\", data);\n            console.log(\"\\uD83D\\uDD0D [MaintenancePieChart] Total maintenance checks received:\", (data === null || data === void 0 ? void 0 : data.length) || 0);\n            if (data) {\n                handleSetMaintenanceChecks(data);\n            } else {\n                console.warn(\"⚠️ [MaintenancePieChart] No data received from GraphQL query\");\n            }\n        },\n        onError: (error)=>{\n            console.error(\"❌ [MaintenancePieChart] queryMaintenanceChecks error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            // loadVessels()\n            loadMaintenanceChecks();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const loadMaintenanceChecks = async function() {\n        let searchFilter = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {\n            ...filter\n        }, searchkeywordFilter = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : keywordFilter;\n        if (searchkeywordFilter.length > 0) {\n            const promises = searchkeywordFilter.map(async (keywordFilter)=>{\n                return await queryMaintenanceChecks({\n                    variables: {\n                        filter: {\n                            ...searchFilter,\n                            ...keywordFilter\n                        }\n                    }\n                });\n            });\n            let responses = await Promise.all(promises);\n            // filter out empty results\n            responses = responses.filter((r)=>r.data.readComponentMaintenanceChecks.nodes.length > 0);\n            // flatten results\n            responses = responses.flatMap((r)=>r.data.readComponentMaintenanceChecks.nodes);\n            // filter out duplicates\n            responses = responses.filter((value, index, self)=>self.findIndex((v)=>v.id === value.id) === index);\n            handleSetMaintenanceChecks(responses);\n        } else {\n            await queryMaintenanceChecks({\n                variables: {\n                    filter: searchFilter\n                }\n            });\n        }\n    };\n    const handleSetMaintenanceChecks = (tasks)=>{\n        const activeTasks = tasks.filter((task)=>task.archived === false).map((task)=>({\n                ...task,\n                isOverDue: (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_2__.isOverDueTask)(task)\n            }));\n        setMaintenanceChecks(activeTasks);\n    // const appendedData: number[] = Array.from(\n    //     new Set(\n    //         activeTasks\n    //             .filter((item: any) => item.assignedToID > 0)\n    //             .map((item: any) => item.assignedToID),\n    //     ),\n    // )\n    // loadCrewMemberInfo(appendedData)\n    };\n    // const [queryCrewMemberInfo] = useLazyQuery(GET_CREW_BY_IDS, {\n    //     fetchPolicy: 'cache-and-network',\n    //     onCompleted: (response: any) => {\n    //         const data = response.readSeaLogsMembers.nodes\n    //         if (data) {\n    //             // setCrewInfo(data)\n    //         }\n    //     },\n    //     onError: (error) => {\n    //         console.error('queryCrewMemberInfo error', error)\n    //     },\n    // })\n    // const loadCrewMemberInfo = async (crewId: any) => {\n    //     await queryCrewMemberInfo({\n    //         variables: {\n    //             crewMemberIDs: crewId.length > 0 ? crewId : [0],\n    //         },\n    //     })\n    // }\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (maintenanceChecks) {\n            setOverdueTasks(maintenanceChecks.filter((task)=>task.isOverDue.status === \"High\" && task.isOverDue.days.includes(\"days\")).length);\n            setUpcomingTasks(maintenanceChecks.filter((task)=>task.isOverDue.status === \"Upcoming\"));\n        }\n    }, [\n        maintenanceChecks\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (upcomingTasks) {\n            setExtractedDays(upcomingTasks.filter((task)=>task.isOverDue.days.includes(\"days\")));\n        }\n    }, [\n        upcomingTasks\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (extractedDays) {\n            setUnder30(extractedDays.filter((task)=>{\n                const match = (task.isOverDue.days || \"\").match(/(\\d+)/);\n                const days = match ? parseInt(match[0], 10) : null;\n                return days !== null && days < 30;\n            }).length);\n            setBetween30and90(extractedDays.filter((task)=>{\n                const match = (task.isOverDue.days || \"\").match(/(\\d+)/);\n                const days = match ? parseInt(match[0], 10) : null;\n                return days !== null && days >= 30 && days <= 90;\n            }).length);\n            setOver90(extractedDays.filter((task)=>{\n                const match = (task.isOverDue.days || \"\").match(/(\\d+)/);\n                const days = match ? parseInt(match[0], 10) : null;\n                return days !== null && days > 90;\n            }).length);\n        }\n    }, [\n        extractedDays\n    ]);\n    const chartData = [\n        {\n            title: \"Tasks overdue\",\n            amount: overdueTasks,\n            fill: \"var(--color-overdue)\",\n            stroke: \"hsl(1, 97%, 60%)\"\n        },\n        {\n            title: \"Tasks due < 30\",\n            amount: under30,\n            fill: \"var(--color-thirtyDays)\",\n            stroke: \"hsl(205, 78%, 48%)\"\n        },\n        {\n            title: \"Tasks due 30 - 90\",\n            amount: between30and90,\n            fill: \"var(--color-thirtyToNinety)\",\n            stroke: \"hsl(205, 32%, 45%)\"\n        },\n        {\n            title: \"Tasks due > 90\",\n            amount: over90,\n            fill: \"var(--color-ninetyPlus)\",\n            stroke: \"hsl(174, 100%, 40%)\"\n        }\n    ];\n    const chartConfig = {\n        amount: {\n            label: \"Amount\"\n        },\n        overdue: {\n            label: \"Tasks overdue\",\n            color: \"var(--chart-1)\"\n        },\n        thirtyDays: {\n            label: \"Tasks due under 30-days\",\n            color: \"var(--chart-3)\"\n        },\n        thirtyToNinety: {\n            label: \"Tasks due 30-90 days\",\n            color: \"var(--chart-4)\"\n        },\n        ninetyPlus: {\n            label: \"Tasks due 90+ days\",\n            color: \"var(--chart-5)\"\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col text-center\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.P, {\n                children: \"Tasks due - days\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\kpis\\\\maintance-pie-chart\\\\maintance-pie-chart.tsx\",\n                lineNumber: 269,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_pie_chart__WEBPACK_IMPORTED_MODULE_4__.PieChartComponent, {\n                chartData: chartData,\n                chartConfig: chartConfig\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\kpis\\\\maintance-pie-chart\\\\maintance-pie-chart.tsx\",\n                lineNumber: 270,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\kpis\\\\maintance-pie-chart\\\\maintance-pie-chart.tsx\",\n        lineNumber: 268,\n        columnNumber: 9\n    }, this);\n}\n_s(MaintenancePieChart, \"M4O8AtZ5QmlugdMuig8xghx4pTE=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_7__.useLazyQuery\n    ];\n});\n_c = MaintenancePieChart;\nvar _c;\n$RefreshReg$(_c, \"MaintenancePieChart\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/kpis/maintance-pie-chart/maintance-pie-chart.tsx\n"));

/***/ })

});