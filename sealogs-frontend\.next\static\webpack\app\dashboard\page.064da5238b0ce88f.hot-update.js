"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/ui/kpis/maintance-pie-chart/maintance-pie-chart.tsx":
/*!*********************************************************************!*\
  !*** ./src/app/ui/kpis/maintance-pie-chart/maintance-pie-chart.tsx ***!
  \*********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ MaintenancePieChart; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _components_pie_chart__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/pie-chart */ \"(app-pages-browser)/./src/components/pie-chart.tsx\");\n/* harmony import */ var _queries__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./queries */ \"(app-pages-browser)/./src/app/ui/kpis/maintance-pie-chart/queries.ts\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction MaintenancePieChart() {\n    _s();\n    const [maintenanceChecks, setMaintenanceChecks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [keywordFilter, setKeywordFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [edit_task, setEdit_task] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [overdueTasks, setOverdueTasks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [upcomingTasks, setUpcomingTasks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [extractedDays, setExtractedDays] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [under30, setUnder30] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [between30and90, setBetween30and90] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [over90, setOver90] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const init_permissions = ()=>{\n        if (permissions) {\n            if ((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_3__.hasPermission)(\"EDIT_TASK\", permissions)) {\n                setEdit_task(true);\n            } else {\n                setEdit_task(false);\n            }\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_3__.getPermissions);\n        init_permissions();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        init_permissions();\n    }, [\n        permissions\n    ]);\n    const [queryMaintenanceChecks] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_8__.useLazyQuery)(_queries__WEBPACK_IMPORTED_MODULE_5__.ReadComponentMaintenanceChecks, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readComponentMaintenanceChecks.nodes;\n            if (data) {\n                handleSetMaintenanceChecks(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryMaintenanceChecks error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            // loadVessels()\n            loadMaintenanceChecks();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const loadMaintenanceChecks = async function() {\n        let searchFilter = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {\n            ...filter\n        }, searchkeywordFilter = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : keywordFilter;\n        if (searchkeywordFilter.length > 0) {\n            const promises = searchkeywordFilter.map(async (keywordFilter)=>{\n                return await queryMaintenanceChecks({\n                    variables: {\n                        filter: {\n                            ...searchFilter,\n                            ...keywordFilter\n                        }\n                    }\n                });\n            });\n            let responses = await Promise.all(promises);\n            // filter out empty results\n            responses = responses.filter((r)=>r.data.readComponentMaintenanceChecks.nodes.length > 0);\n            // flatten results\n            responses = responses.flatMap((r)=>r.data.readComponentMaintenanceChecks.nodes);\n            // filter out duplicates\n            responses = responses.filter((value, index, self)=>self.findIndex((v)=>v.id === value.id) === index);\n            handleSetMaintenanceChecks(responses);\n        } else {\n            await queryMaintenanceChecks({\n                variables: {\n                    filter: searchFilter\n                }\n            });\n        }\n    };\n    const handleSetMaintenanceChecks = (tasks)=>{\n        const activeTasks = tasks.filter((task)=>task.archived === false).map((task)=>({\n                ...task,\n                isOverDue: (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_2__.isOverDueTask)(task)\n            }));\n        setMaintenanceChecks(activeTasks);\n        const appendedData = Array.from(new Set(activeTasks.filter((item)=>item.assignedToID > 0).map((item)=>item.assignedToID)));\n        loadCrewMemberInfo(appendedData);\n    };\n    const [queryCrewMemberInfo] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_8__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_7__.GET_CREW_BY_IDS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readSeaLogsMembers.nodes;\n            if (data) {\n            // setCrewInfo(data)\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryCrewMemberInfo error\", error);\n        }\n    });\n    const loadCrewMemberInfo = async (crewId)=>{\n        await queryCrewMemberInfo({\n            variables: {\n                crewMemberIDs: crewId.length > 0 ? crewId : [\n                    0\n                ]\n            }\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (maintenanceChecks) {\n            setOverdueTasks(maintenanceChecks.filter((task)=>task.isOverDue.status === \"High\" && task.isOverDue.days.includes(\"days\")).length);\n            setUpcomingTasks(maintenanceChecks.filter((task)=>task.isOverDue.status === \"Upcoming\"));\n        }\n    }, [\n        maintenanceChecks\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (upcomingTasks) {\n            setExtractedDays(upcomingTasks.filter((task)=>task.isOverDue.days.includes(\"days\")));\n        }\n    }, [\n        upcomingTasks\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (extractedDays) {\n            setUnder30(extractedDays.filter((task)=>{\n                const match = (task.isOverDue.days || \"\").match(/(\\d+)/);\n                const days = match ? parseInt(match[0], 10) : null;\n                return days !== null && days < 30;\n            }).length);\n            setBetween30and90(extractedDays.filter((task)=>{\n                const match = (task.isOverDue.days || \"\").match(/(\\d+)/);\n                const days = match ? parseInt(match[0], 10) : null;\n                return days !== null && days >= 30 && days <= 90;\n            }).length);\n            setOver90(extractedDays.filter((task)=>{\n                const match = (task.isOverDue.days || \"\").match(/(\\d+)/);\n                const days = match ? parseInt(match[0], 10) : null;\n                return days !== null && days > 90;\n            }).length);\n        }\n    }, [\n        extractedDays\n    ]);\n    const chartData = [\n        {\n            title: \"Tasks overdue\",\n            amount: overdueTasks,\n            fill: \"var(--color-overdue)\",\n            stroke: \"hsl(1, 97%, 60%)\"\n        },\n        {\n            title: \"Tasks due < 30\",\n            amount: under30,\n            fill: \"var(--color-thirtyDays)\",\n            stroke: \"hsl(205, 78%, 48%)\"\n        },\n        {\n            title: \"Tasks due 30 - 90\",\n            amount: between30and90,\n            fill: \"var(--color-thirtyToNinety)\",\n            stroke: \"hsl(205, 32%, 45%)\"\n        },\n        {\n            title: \"Tasks due > 90\",\n            amount: over90,\n            fill: \"var(--color-ninetyPlus)\",\n            stroke: \"hsl(174, 100%, 40%)\"\n        }\n    ];\n    const chartConfig = {\n        amount: {\n            label: \"Amount\"\n        },\n        overdue: {\n            label: \"Tasks overdue\",\n            color: \"var(--chart-1)\"\n        },\n        thirtyDays: {\n            label: \"Tasks due under 30-days\",\n            color: \"var(--chart-3)\"\n        },\n        thirtyToNinety: {\n            label: \"Tasks due 30-90 days\",\n            color: \"var(--chart-4)\"\n        },\n        ninetyPlus: {\n            label: \"Tasks due 90+ days\",\n            color: \"var(--chart-5)\"\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col text-center\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.P, {\n                children: \"Tasks due - days\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\kpis\\\\maintance-pie-chart\\\\maintance-pie-chart.tsx\",\n                lineNumber: 252,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_pie_chart__WEBPACK_IMPORTED_MODULE_4__.PieChartComponent, {\n                chartData: chartData,\n                chartConfig: chartConfig\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\kpis\\\\maintance-pie-chart\\\\maintance-pie-chart.tsx\",\n                lineNumber: 253,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\kpis\\\\maintance-pie-chart\\\\maintance-pie-chart.tsx\",\n        lineNumber: 251,\n        columnNumber: 9\n    }, this);\n}\n_s(MaintenancePieChart, \"nvcwaUqI3ZaRIvwS9WKVI6xzueU=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_8__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_8__.useLazyQuery\n    ];\n});\n_c = MaintenancePieChart;\nvar _c;\n$RefreshReg$(_c, \"MaintenancePieChart\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvdWkva3Bpcy9tYWludGFuY2UtcGllLWNoYXJ0L21haW50YW5jZS1waWUtY2hhcnQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7QUFDa0Q7QUFDRDtBQUNKO0FBQzJCO0FBQ2Q7QUFHQTtBQUN2QjtBQUNzQjtBQUUxQyxTQUFTVzs7SUFDcEIsTUFBTSxDQUFDQyxtQkFBbUJDLHFCQUFxQixHQUFHWCwrQ0FBUUE7SUFFMUQsTUFBTSxDQUFDWSxRQUFRQyxVQUFVLEdBQUdiLCtDQUFRQSxDQUFDLENBQUM7SUFDdEMsTUFBTSxDQUFDYyxXQUFXQyxhQUFhLEdBQUdmLCtDQUFRQSxDQUFDO0lBQzNDLE1BQU0sQ0FBQ2dCLGVBQWVDLGlCQUFpQixHQUFHakIsK0NBQVFBLENBQUMsRUFBRTtJQUNyRCxNQUFNLENBQUNrQixhQUFhQyxlQUFlLEdBQUduQiwrQ0FBUUEsQ0FBTTtJQUNwRCxNQUFNLENBQUNvQixXQUFXQyxhQUFhLEdBQUdyQiwrQ0FBUUEsQ0FBTTtJQUNoRCxNQUFNLENBQUNzQixjQUFjQyxnQkFBZ0IsR0FBR3ZCLCtDQUFRQTtJQUNoRCxNQUFNLENBQUN3QixlQUFlQyxpQkFBaUIsR0FBR3pCLCtDQUFRQTtJQUNsRCxNQUFNLENBQUMwQixlQUFlQyxpQkFBaUIsR0FBRzNCLCtDQUFRQTtJQUNsRCxNQUFNLENBQUM0QixTQUFTQyxXQUFXLEdBQUc3QiwrQ0FBUUE7SUFDdEMsTUFBTSxDQUFDOEIsZ0JBQWdCQyxrQkFBa0IsR0FBRy9CLCtDQUFRQTtJQUNwRCxNQUFNLENBQUNnQyxRQUFRQyxVQUFVLEdBQUdqQywrQ0FBUUE7SUFFcEMsTUFBTWtDLG1CQUFtQjtRQUNyQixJQUFJaEIsYUFBYTtZQUNiLElBQUlkLHNFQUFhQSxDQUFDLGFBQWFjLGNBQWM7Z0JBQ3pDRyxhQUFhO1lBQ2pCLE9BQU87Z0JBQ0hBLGFBQWE7WUFDakI7UUFDSjtJQUNKO0lBRUF0QixnREFBU0EsQ0FBQztRQUNOb0IsZUFBZWhCLG1FQUFjQTtRQUM3QitCO0lBQ0osR0FBRyxFQUFFO0lBRUxuQyxnREFBU0EsQ0FBQztRQUNObUM7SUFDSixHQUFHO1FBQUNoQjtLQUFZO0lBRWhCLE1BQU0sQ0FBQ2lCLHVCQUF1QixHQUFHakMsNERBQVlBLENBQ3pDSSxvRUFBOEJBLEVBQzlCO1FBQ0k4QixhQUFhO1FBQ2JDLGFBQWEsQ0FBQ0M7WUFDVixNQUFNQyxPQUFPRCxTQUFTRSw4QkFBOEIsQ0FBQ0MsS0FBSztZQUMxRCxJQUFJRixNQUFNO2dCQUNORywyQkFBMkJIO1lBQy9CO1FBQ0o7UUFDQUksU0FBUyxDQUFDQztZQUNOQyxRQUFRRCxLQUFLLENBQUMsZ0NBQWdDQTtRQUNsRDtJQUNKO0lBRUo3QyxnREFBU0EsQ0FBQztRQUNOLElBQUllLFdBQVc7WUFDWCxnQkFBZ0I7WUFDaEJnQztZQUNBL0IsYUFBYTtRQUNqQjtJQUNKLEdBQUc7UUFBQ0Q7S0FBVTtJQUNkLE1BQU1nQyx3QkFBd0I7WUFDMUJDLGdGQUE2QjtZQUFFLEdBQUduQyxNQUFNO1FBQUMsR0FDekNvQyx1RkFBMkJoQztRQUUzQixJQUFJZ0Msb0JBQW9CQyxNQUFNLEdBQUcsR0FBRztZQUNoQyxNQUFNQyxXQUFXRixvQkFBb0JHLEdBQUcsQ0FDcEMsT0FBT25DO2dCQUNILE9BQU8sTUFBTW1CLHVCQUF1QjtvQkFDaENpQixXQUFXO3dCQUNQeEMsUUFBUTs0QkFBRSxHQUFHbUMsWUFBWTs0QkFBRSxHQUFHL0IsYUFBYTt3QkFBQztvQkFDaEQ7Z0JBQ0o7WUFDSjtZQUVKLElBQUlxQyxZQUFZLE1BQU1DLFFBQVFDLEdBQUcsQ0FBQ0w7WUFDbEMsMkJBQTJCO1lBQzNCRyxZQUFZQSxVQUFVekMsTUFBTSxDQUN4QixDQUFDNEMsSUFDR0EsRUFBRWpCLElBQUksQ0FBQ0MsOEJBQThCLENBQUNDLEtBQUssQ0FBQ1EsTUFBTSxHQUFHO1lBRTdELGtCQUFrQjtZQUNsQkksWUFBWUEsVUFBVUksT0FBTyxDQUN6QixDQUFDRCxJQUFXQSxFQUFFakIsSUFBSSxDQUFDQyw4QkFBOEIsQ0FBQ0MsS0FBSztZQUUzRCx3QkFBd0I7WUFDeEJZLFlBQVlBLFVBQVV6QyxNQUFNLENBQ3hCLENBQUM4QyxPQUFZQyxPQUFZQyxPQUNyQkEsS0FBS0MsU0FBUyxDQUFDLENBQUNDLElBQVdBLEVBQUVDLEVBQUUsS0FBS0wsTUFBTUssRUFBRSxNQUFNSjtZQUUxRGpCLDJCQUEyQlc7UUFDL0IsT0FBTztZQUNILE1BQU1sQix1QkFBdUI7Z0JBQ3pCaUIsV0FBVztvQkFDUHhDLFFBQVFtQztnQkFDWjtZQUNKO1FBQ0o7SUFDSjtJQUVBLE1BQU1MLDZCQUE2QixDQUFDc0I7UUFDaEMsTUFBTUMsY0FBY0QsTUFDZnBELE1BQU0sQ0FBQyxDQUFDc0QsT0FBY0EsS0FBS0MsUUFBUSxLQUFLLE9BQ3hDaEIsR0FBRyxDQUFDLENBQUNlLE9BQWU7Z0JBQ2pCLEdBQUdBLElBQUk7Z0JBQ1BFLFdBQVduRSwrREFBYUEsQ0FBQ2lFO1lBQzdCO1FBQ0p2RCxxQkFBcUJzRDtRQUNyQixNQUFNSSxlQUF5QkMsTUFBTUMsSUFBSSxDQUNyQyxJQUFJQyxJQUNBUCxZQUNLckQsTUFBTSxDQUFDLENBQUM2RCxPQUFjQSxLQUFLQyxZQUFZLEdBQUcsR0FDMUN2QixHQUFHLENBQUMsQ0FBQ3NCLE9BQWNBLEtBQUtDLFlBQVk7UUFHakRDLG1CQUFtQk47SUFDdkI7SUFFQSxNQUFNLENBQUNPLG9CQUFvQixHQUFHMUUsNERBQVlBLENBQUNNLG1FQUFlQSxFQUFFO1FBQ3hENEIsYUFBYTtRQUNiQyxhQUFhLENBQUNDO1lBQ1YsTUFBTUMsT0FBT0QsU0FBU3VDLGtCQUFrQixDQUFDcEMsS0FBSztZQUM5QyxJQUFJRixNQUFNO1lBQ04sb0JBQW9CO1lBQ3hCO1FBQ0o7UUFDQUksU0FBUyxDQUFDQztZQUNOQyxRQUFRRCxLQUFLLENBQUMsNkJBQTZCQTtRQUMvQztJQUNKO0lBQ0EsTUFBTStCLHFCQUFxQixPQUFPRztRQUM5QixNQUFNRixvQkFBb0I7WUFDdEJ4QixXQUFXO2dCQUNQMkIsZUFBZUQsT0FBTzdCLE1BQU0sR0FBRyxJQUFJNkIsU0FBUztvQkFBQztpQkFBRTtZQUNuRDtRQUNKO0lBQ0o7SUFFQS9FLGdEQUFTQSxDQUFDO1FBQ04sSUFBSVcsbUJBQW1CO1lBQ25CYSxnQkFDSWIsa0JBQWtCRSxNQUFNLENBQ3BCLENBQUNzRCxPQUNHQSxLQUFLRSxTQUFTLENBQUNZLE1BQU0sS0FBSyxVQUMxQmQsS0FBS0UsU0FBUyxDQUFDYSxJQUFJLENBQUNDLFFBQVEsQ0FBQyxTQUNuQ2pDLE1BQU07WUFFWnhCLGlCQUNJZixrQkFBa0JFLE1BQU0sQ0FDcEIsQ0FBQ3NELE9BQWNBLEtBQUtFLFNBQVMsQ0FBQ1ksTUFBTSxLQUFLO1FBR3JEO0lBQ0osR0FBRztRQUFDdEU7S0FBa0I7SUFFdEJYLGdEQUFTQSxDQUFDO1FBQ04sSUFBSXlCLGVBQWU7WUFDZkcsaUJBQ0lILGNBQWNaLE1BQU0sQ0FBQyxDQUFDc0QsT0FDbEJBLEtBQUtFLFNBQVMsQ0FBQ2EsSUFBSSxDQUFDQyxRQUFRLENBQUM7UUFHekM7SUFDSixHQUFHO1FBQUMxRDtLQUFjO0lBRWxCekIsZ0RBQVNBLENBQUM7UUFDTixJQUFJMkIsZUFBZTtZQUNmRyxXQUNJSCxjQUFjZCxNQUFNLENBQUMsQ0FBQ3NEO2dCQUNsQixNQUFNaUIsUUFBUSxDQUFDakIsS0FBS0UsU0FBUyxDQUFDYSxJQUFJLElBQUksRUFBQyxFQUFHRSxLQUFLLENBQUM7Z0JBQ2hELE1BQU1GLE9BQU9FLFFBQVFDLFNBQVNELEtBQUssQ0FBQyxFQUFFLEVBQUUsTUFBTTtnQkFDOUMsT0FBT0YsU0FBUyxRQUFRQSxPQUFPO1lBQ25DLEdBQUdoQyxNQUFNO1lBR2JsQixrQkFDSUwsY0FBY2QsTUFBTSxDQUFDLENBQUNzRDtnQkFDbEIsTUFBTWlCLFFBQVEsQ0FBQ2pCLEtBQUtFLFNBQVMsQ0FBQ2EsSUFBSSxJQUFJLEVBQUMsRUFBR0UsS0FBSyxDQUFDO2dCQUNoRCxNQUFNRixPQUFPRSxRQUFRQyxTQUFTRCxLQUFLLENBQUMsRUFBRSxFQUFFLE1BQU07Z0JBQzlDLE9BQU9GLFNBQVMsUUFBUUEsUUFBUSxNQUFNQSxRQUFRO1lBQ2xELEdBQUdoQyxNQUFNO1lBR2JoQixVQUNJUCxjQUFjZCxNQUFNLENBQUMsQ0FBQ3NEO2dCQUNsQixNQUFNaUIsUUFBUSxDQUFDakIsS0FBS0UsU0FBUyxDQUFDYSxJQUFJLElBQUksRUFBQyxFQUFHRSxLQUFLLENBQUM7Z0JBQ2hELE1BQU1GLE9BQU9FLFFBQVFDLFNBQVNELEtBQUssQ0FBQyxFQUFFLEVBQUUsTUFBTTtnQkFDOUMsT0FBT0YsU0FBUyxRQUFRQSxPQUFPO1lBQ25DLEdBQUdoQyxNQUFNO1FBRWpCO0lBQ0osR0FBRztRQUFDdkI7S0FBYztJQUVsQixNQUFNMkQsWUFBWTtRQUNkO1lBQ0lDLE9BQU87WUFDUEMsUUFBUWpFO1lBQ1JrRSxNQUFNO1lBQ05DLFFBQVE7UUFDWjtRQUNBO1lBQ0lILE9BQU87WUFDUEMsUUFBUTNEO1lBQ1I0RCxNQUFNO1lBQ05DLFFBQVE7UUFDWjtRQUNBO1lBQ0lILE9BQU87WUFDUEMsUUFBUXpEO1lBQ1IwRCxNQUFNO1lBQ05DLFFBQVE7UUFDWjtRQUNBO1lBQ0lILE9BQU87WUFDUEMsUUFBUXZEO1lBQ1J3RCxNQUFNO1lBQ05DLFFBQVE7UUFDWjtLQUVIO0lBQ0QsTUFBTUMsY0FBYztRQUNoQkgsUUFBUTtZQUNKSSxPQUFPO1FBQ1g7UUFDQUMsU0FBUztZQUNMRCxPQUFPO1lBQ1BFLE9BQU87UUFDWDtRQUNBQyxZQUFZO1lBQ1JILE9BQU87WUFDUEUsT0FBTztRQUNYO1FBQ0FFLGdCQUFnQjtZQUNaSixPQUFPO1lBQ1BFLE9BQU87UUFDWDtRQUNBRyxZQUFZO1lBQ1JMLE9BQU87WUFDUEUsT0FBTztRQUNYO0lBQ0o7SUFFQSxxQkFDSSw4REFBQ0k7UUFBSUMsV0FBVTs7MEJBQ1gsOERBQUMzRiw2Q0FBQ0E7MEJBQUM7Ozs7OzswQkFDSCw4REFBQ0Ysb0VBQWlCQTtnQkFDZGdGLFdBQVdBO2dCQUNYSyxhQUFhQTs7Ozs7Ozs7Ozs7O0FBSTdCO0dBdFB3QmpGOztRQWtDYVAsd0RBQVlBO1FBK0VmQSx3REFBWUE7OztLQWpIdEJPIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvdWkva3Bpcy9tYWludGFuY2UtcGllLWNoYXJ0L21haW50YW5jZS1waWUtY2hhcnQudHN4PzcxNDYiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXHJcbmltcG9ydCBSZWFjdCwgeyB1c2VFZmZlY3QsIHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnXHJcbmltcG9ydCB7IGlzT3ZlckR1ZVRhc2sgfSBmcm9tICdAL2FwcC9saWIvYWN0aW9ucydcclxuaW1wb3J0IHsgdXNlTGF6eVF1ZXJ5IH0gZnJvbSAnQGFwb2xsby9jbGllbnQnXHJcbmltcG9ydCB7IGdldFBlcm1pc3Npb25zLCBoYXNQZXJtaXNzaW9uIH0gZnJvbSAnQC9hcHAvaGVscGVycy91c2VySGVscGVyJ1xyXG5pbXBvcnQgeyBQaWVDaGFydENvbXBvbmVudCB9IGZyb20gJ0AvY29tcG9uZW50cy9waWUtY2hhcnQnXHJcbmltcG9ydCB7IENoYXJ0Q29uZmlnIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2NoYXJ0J1xyXG5pbXBvcnQgeyBDYXJkRGVzY3JpcHRpb24sIENhcmRIZWFkZXIsIENhcmRUaXRsZSB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9jYXJkJ1xyXG5pbXBvcnQgeyBSZWFkQ29tcG9uZW50TWFpbnRlbmFuY2VDaGVja3MgfSBmcm9tICcuL3F1ZXJpZXMnXHJcbmltcG9ydCB7IFAgfSBmcm9tICdAL2NvbXBvbmVudHMvdWknXHJcbmltcG9ydCB7IEdFVF9DUkVXX0JZX0lEUyB9IGZyb20gJ0AvYXBwL2xpYi9ncmFwaFFML3F1ZXJ5J1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTWFpbnRlbmFuY2VQaWVDaGFydCgpIHtcclxuICAgIGNvbnN0IFttYWludGVuYW5jZUNoZWNrcywgc2V0TWFpbnRlbmFuY2VDaGVja3NdID0gdXNlU3RhdGU8YW55PigpXHJcblxyXG4gICAgY29uc3QgW2ZpbHRlciwgc2V0RmlsdGVyXSA9IHVzZVN0YXRlKHt9IGFzIFNlYXJjaEZpbHRlcilcclxuICAgIGNvbnN0IFtpc0xvYWRpbmcsIHNldElzTG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKVxyXG4gICAgY29uc3QgW2tleXdvcmRGaWx0ZXIsIHNldEtleXdvcmRGaWx0ZXJdID0gdXNlU3RhdGUoW10gYXMgYW55KVxyXG4gICAgY29uc3QgW3Blcm1pc3Npb25zLCBzZXRQZXJtaXNzaW9uc10gPSB1c2VTdGF0ZTxhbnk+KGZhbHNlKVxyXG4gICAgY29uc3QgW2VkaXRfdGFzaywgc2V0RWRpdF90YXNrXSA9IHVzZVN0YXRlPGFueT4oZmFsc2UpXHJcbiAgICBjb25zdCBbb3ZlcmR1ZVRhc2tzLCBzZXRPdmVyZHVlVGFza3NdID0gdXNlU3RhdGU8YW55PigpXHJcbiAgICBjb25zdCBbdXBjb21pbmdUYXNrcywgc2V0VXBjb21pbmdUYXNrc10gPSB1c2VTdGF0ZTxhbnk+KClcclxuICAgIGNvbnN0IFtleHRyYWN0ZWREYXlzLCBzZXRFeHRyYWN0ZWREYXlzXSA9IHVzZVN0YXRlPGFueT4oKVxyXG4gICAgY29uc3QgW3VuZGVyMzAsIHNldFVuZGVyMzBdID0gdXNlU3RhdGU8YW55PigpXHJcbiAgICBjb25zdCBbYmV0d2VlbjMwYW5kOTAsIHNldEJldHdlZW4zMGFuZDkwXSA9IHVzZVN0YXRlPGFueT4oKVxyXG4gICAgY29uc3QgW292ZXI5MCwgc2V0T3ZlcjkwXSA9IHVzZVN0YXRlPGFueT4oKVxyXG5cclxuICAgIGNvbnN0IGluaXRfcGVybWlzc2lvbnMgPSAoKSA9PiB7XHJcbiAgICAgICAgaWYgKHBlcm1pc3Npb25zKSB7XHJcbiAgICAgICAgICAgIGlmIChoYXNQZXJtaXNzaW9uKCdFRElUX1RBU0snLCBwZXJtaXNzaW9ucykpIHtcclxuICAgICAgICAgICAgICAgIHNldEVkaXRfdGFzayh0cnVlKVxyXG4gICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgc2V0RWRpdF90YXNrKGZhbHNlKVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICAgICAgc2V0UGVybWlzc2lvbnMoZ2V0UGVybWlzc2lvbnMpXHJcbiAgICAgICAgaW5pdF9wZXJtaXNzaW9ucygpXHJcbiAgICB9LCBbXSlcclxuXHJcbiAgICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgICAgIGluaXRfcGVybWlzc2lvbnMoKVxyXG4gICAgfSwgW3Blcm1pc3Npb25zXSlcclxuXHJcbiAgICBjb25zdCBbcXVlcnlNYWludGVuYW5jZUNoZWNrc10gPSB1c2VMYXp5UXVlcnkoXHJcbiAgICAgICAgUmVhZENvbXBvbmVudE1haW50ZW5hbmNlQ2hlY2tzLFxyXG4gICAgICAgIHtcclxuICAgICAgICAgICAgZmV0Y2hQb2xpY3k6ICdjYWNoZS1hbmQtbmV0d29yaycsXHJcbiAgICAgICAgICAgIG9uQ29tcGxldGVkOiAocmVzcG9uc2U6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgY29uc3QgZGF0YSA9IHJlc3BvbnNlLnJlYWRDb21wb25lbnRNYWludGVuYW5jZUNoZWNrcy5ub2Rlc1xyXG4gICAgICAgICAgICAgICAgaWYgKGRhdGEpIHtcclxuICAgICAgICAgICAgICAgICAgICBoYW5kbGVTZXRNYWludGVuYW5jZUNoZWNrcyhkYXRhKVxyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICBvbkVycm9yOiAoZXJyb3I6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcigncXVlcnlNYWludGVuYW5jZUNoZWNrcyBlcnJvcicsIGVycm9yKVxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgIH0sXHJcbiAgICApXHJcbiAgICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgICAgIGlmIChpc0xvYWRpbmcpIHtcclxuICAgICAgICAgICAgLy8gbG9hZFZlc3NlbHMoKVxyXG4gICAgICAgICAgICBsb2FkTWFpbnRlbmFuY2VDaGVja3MoKVxyXG4gICAgICAgICAgICBzZXRJc0xvYWRpbmcoZmFsc2UpXHJcbiAgICAgICAgfVxyXG4gICAgfSwgW2lzTG9hZGluZ10pXHJcbiAgICBjb25zdCBsb2FkTWFpbnRlbmFuY2VDaGVja3MgPSBhc3luYyAoXHJcbiAgICAgICAgc2VhcmNoRmlsdGVyOiBTZWFyY2hGaWx0ZXIgPSB7IC4uLmZpbHRlciB9LFxyXG4gICAgICAgIHNlYXJjaGtleXdvcmRGaWx0ZXI6IGFueSA9IGtleXdvcmRGaWx0ZXIsXHJcbiAgICApID0+IHtcclxuICAgICAgICBpZiAoc2VhcmNoa2V5d29yZEZpbHRlci5sZW5ndGggPiAwKSB7XHJcbiAgICAgICAgICAgIGNvbnN0IHByb21pc2VzID0gc2VhcmNoa2V5d29yZEZpbHRlci5tYXAoXHJcbiAgICAgICAgICAgICAgICBhc3luYyAoa2V5d29yZEZpbHRlcjogYW55KSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGF3YWl0IHF1ZXJ5TWFpbnRlbmFuY2VDaGVja3Moe1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB2YXJpYWJsZXM6IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZpbHRlcjogeyAuLi5zZWFyY2hGaWx0ZXIsIC4uLmtleXdvcmRGaWx0ZXIgfSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICBsZXQgcmVzcG9uc2VzID0gYXdhaXQgUHJvbWlzZS5hbGwocHJvbWlzZXMpXHJcbiAgICAgICAgICAgIC8vIGZpbHRlciBvdXQgZW1wdHkgcmVzdWx0c1xyXG4gICAgICAgICAgICByZXNwb25zZXMgPSByZXNwb25zZXMuZmlsdGVyKFxyXG4gICAgICAgICAgICAgICAgKHI6IGFueSkgPT5cclxuICAgICAgICAgICAgICAgICAgICByLmRhdGEucmVhZENvbXBvbmVudE1haW50ZW5hbmNlQ2hlY2tzLm5vZGVzLmxlbmd0aCA+IDAsXHJcbiAgICAgICAgICAgIClcclxuICAgICAgICAgICAgLy8gZmxhdHRlbiByZXN1bHRzXHJcbiAgICAgICAgICAgIHJlc3BvbnNlcyA9IHJlc3BvbnNlcy5mbGF0TWFwKFxyXG4gICAgICAgICAgICAgICAgKHI6IGFueSkgPT4gci5kYXRhLnJlYWRDb21wb25lbnRNYWludGVuYW5jZUNoZWNrcy5ub2RlcyxcclxuICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICAvLyBmaWx0ZXIgb3V0IGR1cGxpY2F0ZXNcclxuICAgICAgICAgICAgcmVzcG9uc2VzID0gcmVzcG9uc2VzLmZpbHRlcihcclxuICAgICAgICAgICAgICAgICh2YWx1ZTogYW55LCBpbmRleDogYW55LCBzZWxmOiBhbnkpID0+XHJcbiAgICAgICAgICAgICAgICAgICAgc2VsZi5maW5kSW5kZXgoKHY6IGFueSkgPT4gdi5pZCA9PT0gdmFsdWUuaWQpID09PSBpbmRleCxcclxuICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICBoYW5kbGVTZXRNYWludGVuYW5jZUNoZWNrcyhyZXNwb25zZXMpXHJcbiAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgYXdhaXQgcXVlcnlNYWludGVuYW5jZUNoZWNrcyh7XHJcbiAgICAgICAgICAgICAgICB2YXJpYWJsZXM6IHtcclxuICAgICAgICAgICAgICAgICAgICBmaWx0ZXI6IHNlYXJjaEZpbHRlcixcclxuICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIH0pXHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIGNvbnN0IGhhbmRsZVNldE1haW50ZW5hbmNlQ2hlY2tzID0gKHRhc2tzOiBhbnkpID0+IHtcclxuICAgICAgICBjb25zdCBhY3RpdmVUYXNrcyA9IHRhc2tzXHJcbiAgICAgICAgICAgIC5maWx0ZXIoKHRhc2s6IGFueSkgPT4gdGFzay5hcmNoaXZlZCA9PT0gZmFsc2UpXHJcbiAgICAgICAgICAgIC5tYXAoKHRhc2s6IGFueSkgPT4gKHtcclxuICAgICAgICAgICAgICAgIC4uLnRhc2ssXHJcbiAgICAgICAgICAgICAgICBpc092ZXJEdWU6IGlzT3ZlckR1ZVRhc2sodGFzayksXHJcbiAgICAgICAgICAgIH0pKVxyXG4gICAgICAgIHNldE1haW50ZW5hbmNlQ2hlY2tzKGFjdGl2ZVRhc2tzKVxyXG4gICAgICAgIGNvbnN0IGFwcGVuZGVkRGF0YTogbnVtYmVyW10gPSBBcnJheS5mcm9tKFxyXG4gICAgICAgICAgICBuZXcgU2V0KFxyXG4gICAgICAgICAgICAgICAgYWN0aXZlVGFza3NcclxuICAgICAgICAgICAgICAgICAgICAuZmlsdGVyKChpdGVtOiBhbnkpID0+IGl0ZW0uYXNzaWduZWRUb0lEID4gMClcclxuICAgICAgICAgICAgICAgICAgICAubWFwKChpdGVtOiBhbnkpID0+IGl0ZW0uYXNzaWduZWRUb0lEKSxcclxuICAgICAgICAgICAgKSxcclxuICAgICAgICApXHJcbiAgICAgICAgbG9hZENyZXdNZW1iZXJJbmZvKGFwcGVuZGVkRGF0YSlcclxuICAgIH1cclxuXHJcbiAgICBjb25zdCBbcXVlcnlDcmV3TWVtYmVySW5mb10gPSB1c2VMYXp5UXVlcnkoR0VUX0NSRVdfQllfSURTLCB7XHJcbiAgICAgICAgZmV0Y2hQb2xpY3k6ICdjYWNoZS1hbmQtbmV0d29yaycsXHJcbiAgICAgICAgb25Db21wbGV0ZWQ6IChyZXNwb25zZTogYW55KSA9PiB7XHJcbiAgICAgICAgICAgIGNvbnN0IGRhdGEgPSByZXNwb25zZS5yZWFkU2VhTG9nc01lbWJlcnMubm9kZXNcclxuICAgICAgICAgICAgaWYgKGRhdGEpIHtcclxuICAgICAgICAgICAgICAgIC8vIHNldENyZXdJbmZvKGRhdGEpXHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9LFxyXG4gICAgICAgIG9uRXJyb3I6IChlcnJvcikgPT4ge1xyXG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKCdxdWVyeUNyZXdNZW1iZXJJbmZvIGVycm9yJywgZXJyb3IpXHJcbiAgICAgICAgfSxcclxuICAgIH0pXHJcbiAgICBjb25zdCBsb2FkQ3Jld01lbWJlckluZm8gPSBhc3luYyAoY3Jld0lkOiBhbnkpID0+IHtcclxuICAgICAgICBhd2FpdCBxdWVyeUNyZXdNZW1iZXJJbmZvKHtcclxuICAgICAgICAgICAgdmFyaWFibGVzOiB7XHJcbiAgICAgICAgICAgICAgICBjcmV3TWVtYmVySURzOiBjcmV3SWQubGVuZ3RoID4gMCA/IGNyZXdJZCA6IFswXSxcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICB9KVxyXG4gICAgfVxyXG5cclxuICAgIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICAgICAgaWYgKG1haW50ZW5hbmNlQ2hlY2tzKSB7XHJcbiAgICAgICAgICAgIHNldE92ZXJkdWVUYXNrcyhcclxuICAgICAgICAgICAgICAgIG1haW50ZW5hbmNlQ2hlY2tzLmZpbHRlcihcclxuICAgICAgICAgICAgICAgICAgICAodGFzazogYW55KSA9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICB0YXNrLmlzT3ZlckR1ZS5zdGF0dXMgPT09ICdIaWdoJyAmJlxyXG4gICAgICAgICAgICAgICAgICAgICAgICB0YXNrLmlzT3ZlckR1ZS5kYXlzLmluY2x1ZGVzKCdkYXlzJyksXHJcbiAgICAgICAgICAgICAgICApLmxlbmd0aCxcclxuICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICBzZXRVcGNvbWluZ1Rhc2tzKFxyXG4gICAgICAgICAgICAgICAgbWFpbnRlbmFuY2VDaGVja3MuZmlsdGVyKFxyXG4gICAgICAgICAgICAgICAgICAgICh0YXNrOiBhbnkpID0+IHRhc2suaXNPdmVyRHVlLnN0YXR1cyA9PT0gJ1VwY29taW5nJyxcclxuICAgICAgICAgICAgICAgICksXHJcbiAgICAgICAgICAgIClcclxuICAgICAgICB9XHJcbiAgICB9LCBbbWFpbnRlbmFuY2VDaGVja3NdKVxyXG5cclxuICAgIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICAgICAgaWYgKHVwY29taW5nVGFza3MpIHtcclxuICAgICAgICAgICAgc2V0RXh0cmFjdGVkRGF5cyhcclxuICAgICAgICAgICAgICAgIHVwY29taW5nVGFza3MuZmlsdGVyKCh0YXNrOiBhbnkpID0+XHJcbiAgICAgICAgICAgICAgICAgICAgdGFzay5pc092ZXJEdWUuZGF5cy5pbmNsdWRlcygnZGF5cycpLFxyXG4gICAgICAgICAgICAgICAgKSxcclxuICAgICAgICAgICAgKVxyXG4gICAgICAgIH1cclxuICAgIH0sIFt1cGNvbWluZ1Rhc2tzXSlcclxuXHJcbiAgICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgICAgIGlmIChleHRyYWN0ZWREYXlzKSB7XHJcbiAgICAgICAgICAgIHNldFVuZGVyMzAoXHJcbiAgICAgICAgICAgICAgICBleHRyYWN0ZWREYXlzLmZpbHRlcigodGFzazogYW55KSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgbWF0Y2ggPSAodGFzay5pc092ZXJEdWUuZGF5cyB8fCAnJykubWF0Y2goLyhcXGQrKS8pXHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgZGF5cyA9IG1hdGNoID8gcGFyc2VJbnQobWF0Y2hbMF0sIDEwKSA6IG51bGxcclxuICAgICAgICAgICAgICAgICAgICByZXR1cm4gZGF5cyAhPT0gbnVsbCAmJiBkYXlzIDwgMzBcclxuICAgICAgICAgICAgICAgIH0pLmxlbmd0aCxcclxuICAgICAgICAgICAgKVxyXG5cclxuICAgICAgICAgICAgc2V0QmV0d2VlbjMwYW5kOTAoXHJcbiAgICAgICAgICAgICAgICBleHRyYWN0ZWREYXlzLmZpbHRlcigodGFzazogYW55KSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgbWF0Y2ggPSAodGFzay5pc092ZXJEdWUuZGF5cyB8fCAnJykubWF0Y2goLyhcXGQrKS8pXHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgZGF5cyA9IG1hdGNoID8gcGFyc2VJbnQobWF0Y2hbMF0sIDEwKSA6IG51bGxcclxuICAgICAgICAgICAgICAgICAgICByZXR1cm4gZGF5cyAhPT0gbnVsbCAmJiBkYXlzID49IDMwICYmIGRheXMgPD0gOTBcclxuICAgICAgICAgICAgICAgIH0pLmxlbmd0aCxcclxuICAgICAgICAgICAgKVxyXG5cclxuICAgICAgICAgICAgc2V0T3ZlcjkwKFxyXG4gICAgICAgICAgICAgICAgZXh0cmFjdGVkRGF5cy5maWx0ZXIoKHRhc2s6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IG1hdGNoID0gKHRhc2suaXNPdmVyRHVlLmRheXMgfHwgJycpLm1hdGNoKC8oXFxkKykvKVxyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGRheXMgPSBtYXRjaCA/IHBhcnNlSW50KG1hdGNoWzBdLCAxMCkgOiBudWxsXHJcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGRheXMgIT09IG51bGwgJiYgZGF5cyA+IDkwXHJcbiAgICAgICAgICAgICAgICB9KS5sZW5ndGgsXHJcbiAgICAgICAgICAgIClcclxuICAgICAgICB9XHJcbiAgICB9LCBbZXh0cmFjdGVkRGF5c10pXHJcblxyXG4gICAgY29uc3QgY2hhcnREYXRhID0gW1xyXG4gICAgICAgIHtcclxuICAgICAgICAgICAgdGl0bGU6ICdUYXNrcyBvdmVyZHVlJyxcclxuICAgICAgICAgICAgYW1vdW50OiBvdmVyZHVlVGFza3MsXHJcbiAgICAgICAgICAgIGZpbGw6ICd2YXIoLS1jb2xvci1vdmVyZHVlKScsXHJcbiAgICAgICAgICAgIHN0cm9rZTogJ2hzbCgxLCA5NyUsIDYwJSknLFxyXG4gICAgICAgIH0sXHJcbiAgICAgICAge1xyXG4gICAgICAgICAgICB0aXRsZTogJ1Rhc2tzIGR1ZSA8IDMwJyxcclxuICAgICAgICAgICAgYW1vdW50OiB1bmRlcjMwLFxyXG4gICAgICAgICAgICBmaWxsOiAndmFyKC0tY29sb3ItdGhpcnR5RGF5cyknLFxyXG4gICAgICAgICAgICBzdHJva2U6ICdoc2woMjA1LCA3OCUsIDQ4JSknLFxyXG4gICAgICAgIH0sXHJcbiAgICAgICAge1xyXG4gICAgICAgICAgICB0aXRsZTogJ1Rhc2tzIGR1ZSAzMCAtIDkwJyxcclxuICAgICAgICAgICAgYW1vdW50OiBiZXR3ZWVuMzBhbmQ5MCxcclxuICAgICAgICAgICAgZmlsbDogJ3ZhcigtLWNvbG9yLXRoaXJ0eVRvTmluZXR5KScsXHJcbiAgICAgICAgICAgIHN0cm9rZTogJ2hzbCgyMDUsIDMyJSwgNDUlKScsXHJcbiAgICAgICAgfSxcclxuICAgICAgICB7XHJcbiAgICAgICAgICAgIHRpdGxlOiAnVGFza3MgZHVlID4gOTAnLFxyXG4gICAgICAgICAgICBhbW91bnQ6IG92ZXI5MCxcclxuICAgICAgICAgICAgZmlsbDogJ3ZhcigtLWNvbG9yLW5pbmV0eVBsdXMpJyxcclxuICAgICAgICAgICAgc3Ryb2tlOiAnaHNsKDE3NCwgMTAwJSwgNDAlKScsXHJcbiAgICAgICAgfSxcclxuICAgICAgICAvL3sgdGl0bGU6IFwiQ29tcGxldGVkXCIsIGFtb3VudDogMiwgZmlsbDogXCJ2YXIoLS1jb2xvci1jb21wbGV0ZWQpXCIgfSxcclxuICAgIF1cclxuICAgIGNvbnN0IGNoYXJ0Q29uZmlnID0ge1xyXG4gICAgICAgIGFtb3VudDoge1xyXG4gICAgICAgICAgICBsYWJlbDogJ0Ftb3VudCcsXHJcbiAgICAgICAgfSxcclxuICAgICAgICBvdmVyZHVlOiB7XHJcbiAgICAgICAgICAgIGxhYmVsOiAnVGFza3Mgb3ZlcmR1ZScsXHJcbiAgICAgICAgICAgIGNvbG9yOiAndmFyKC0tY2hhcnQtMSknLFxyXG4gICAgICAgIH0sXHJcbiAgICAgICAgdGhpcnR5RGF5czoge1xyXG4gICAgICAgICAgICBsYWJlbDogJ1Rhc2tzIGR1ZSB1bmRlciAzMC1kYXlzJyxcclxuICAgICAgICAgICAgY29sb3I6ICd2YXIoLS1jaGFydC0zKScsXHJcbiAgICAgICAgfSxcclxuICAgICAgICB0aGlydHlUb05pbmV0eToge1xyXG4gICAgICAgICAgICBsYWJlbDogJ1Rhc2tzIGR1ZSAzMC05MCBkYXlzJyxcclxuICAgICAgICAgICAgY29sb3I6ICd2YXIoLS1jaGFydC00KScsXHJcbiAgICAgICAgfSxcclxuICAgICAgICBuaW5ldHlQbHVzOiB7XHJcbiAgICAgICAgICAgIGxhYmVsOiAnVGFza3MgZHVlIDkwKyBkYXlzJyxcclxuICAgICAgICAgICAgY29sb3I6ICd2YXIoLS1jaGFydC01KScsXHJcbiAgICAgICAgfSxcclxuICAgIH0gc2F0aXNmaWVzIENoYXJ0Q29uZmlnXHJcblxyXG4gICAgcmV0dXJuIChcclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT0nZmxleCBmbGV4LWNvbCB0ZXh0LWNlbnRlcic+XHJcbiAgICAgICAgICAgIDxQPlRhc2tzIGR1ZSAtIGRheXM8L1A+XHJcbiAgICAgICAgICAgIDxQaWVDaGFydENvbXBvbmVudFxyXG4gICAgICAgICAgICAgICAgY2hhcnREYXRhPXtjaGFydERhdGF9XHJcbiAgICAgICAgICAgICAgICBjaGFydENvbmZpZz17Y2hhcnRDb25maWd9XHJcbiAgICAgICAgICAgIC8+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICApXHJcbn1cclxuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlRWZmZWN0IiwidXNlU3RhdGUiLCJpc092ZXJEdWVUYXNrIiwidXNlTGF6eVF1ZXJ5IiwiZ2V0UGVybWlzc2lvbnMiLCJoYXNQZXJtaXNzaW9uIiwiUGllQ2hhcnRDb21wb25lbnQiLCJSZWFkQ29tcG9uZW50TWFpbnRlbmFuY2VDaGVja3MiLCJQIiwiR0VUX0NSRVdfQllfSURTIiwiTWFpbnRlbmFuY2VQaWVDaGFydCIsIm1haW50ZW5hbmNlQ2hlY2tzIiwic2V0TWFpbnRlbmFuY2VDaGVja3MiLCJmaWx0ZXIiLCJzZXRGaWx0ZXIiLCJpc0xvYWRpbmciLCJzZXRJc0xvYWRpbmciLCJrZXl3b3JkRmlsdGVyIiwic2V0S2V5d29yZEZpbHRlciIsInBlcm1pc3Npb25zIiwic2V0UGVybWlzc2lvbnMiLCJlZGl0X3Rhc2siLCJzZXRFZGl0X3Rhc2siLCJvdmVyZHVlVGFza3MiLCJzZXRPdmVyZHVlVGFza3MiLCJ1cGNvbWluZ1Rhc2tzIiwic2V0VXBjb21pbmdUYXNrcyIsImV4dHJhY3RlZERheXMiLCJzZXRFeHRyYWN0ZWREYXlzIiwidW5kZXIzMCIsInNldFVuZGVyMzAiLCJiZXR3ZWVuMzBhbmQ5MCIsInNldEJldHdlZW4zMGFuZDkwIiwib3ZlcjkwIiwic2V0T3ZlcjkwIiwiaW5pdF9wZXJtaXNzaW9ucyIsInF1ZXJ5TWFpbnRlbmFuY2VDaGVja3MiLCJmZXRjaFBvbGljeSIsIm9uQ29tcGxldGVkIiwicmVzcG9uc2UiLCJkYXRhIiwicmVhZENvbXBvbmVudE1haW50ZW5hbmNlQ2hlY2tzIiwibm9kZXMiLCJoYW5kbGVTZXRNYWludGVuYW5jZUNoZWNrcyIsIm9uRXJyb3IiLCJlcnJvciIsImNvbnNvbGUiLCJsb2FkTWFpbnRlbmFuY2VDaGVja3MiLCJzZWFyY2hGaWx0ZXIiLCJzZWFyY2hrZXl3b3JkRmlsdGVyIiwibGVuZ3RoIiwicHJvbWlzZXMiLCJtYXAiLCJ2YXJpYWJsZXMiLCJyZXNwb25zZXMiLCJQcm9taXNlIiwiYWxsIiwiciIsImZsYXRNYXAiLCJ2YWx1ZSIsImluZGV4Iiwic2VsZiIsImZpbmRJbmRleCIsInYiLCJpZCIsInRhc2tzIiwiYWN0aXZlVGFza3MiLCJ0YXNrIiwiYXJjaGl2ZWQiLCJpc092ZXJEdWUiLCJhcHBlbmRlZERhdGEiLCJBcnJheSIsImZyb20iLCJTZXQiLCJpdGVtIiwiYXNzaWduZWRUb0lEIiwibG9hZENyZXdNZW1iZXJJbmZvIiwicXVlcnlDcmV3TWVtYmVySW5mbyIsInJlYWRTZWFMb2dzTWVtYmVycyIsImNyZXdJZCIsImNyZXdNZW1iZXJJRHMiLCJzdGF0dXMiLCJkYXlzIiwiaW5jbHVkZXMiLCJtYXRjaCIsInBhcnNlSW50IiwiY2hhcnREYXRhIiwidGl0bGUiLCJhbW91bnQiLCJmaWxsIiwic3Ryb2tlIiwiY2hhcnRDb25maWciLCJsYWJlbCIsIm92ZXJkdWUiLCJjb2xvciIsInRoaXJ0eURheXMiLCJ0aGlydHlUb05pbmV0eSIsIm5pbmV0eVBsdXMiLCJkaXYiLCJjbGFzc05hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/kpis/maintance-pie-chart/maintance-pie-chart.tsx\n"));

/***/ })

});